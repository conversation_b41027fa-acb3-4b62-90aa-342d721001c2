import * as React from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { SideBarMenuData, SideBarMenuDataHoly } from '@/config/sideBarMenu';
import styled from 'styled-components';
import { TreeIconMap } from '@/config/TreeIconMap';
import { Link, useLocation, useOutletContext } from 'react-router-dom';
import { ListMenuItem } from '@/types/Layout';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useMenuSearch } from '@/hooks/useMenuSearch';
import SearchResults from './SearchableListView';

const SidebarListRoot = styled.div`
  a {
    color: inherit;
    text-decoration: none;
  }
  a:hover {
    color: ${(props) => props.theme.palette.primary.main};
  }
  .icon {
    font-size: 18px;
    @media ${breakPointsMaxwidth.md} {
      font-size: 22px;
    }
  }
  .active {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    color: ${(props) => props.theme.palette.primary.main};
    border-right: 2.5px solid;
  }
  .inactive {
    /* color: ${(props) => props.theme.palette.grey[900]}; */
  }
  /* .MuiListItemText-root {
    font-family: 'Poppins bold';
    font-weight: 600;
  } */
`;
type StyledListItemProps = {
  list: ListMenuItem;
  location: any;
};

function StyledListItem(props: StyledListItemProps) {
  const { list, location } = props;
  const Icon = list.icon ? TreeIconMap[list.icon] : null;
  return (
    <ListItemButton
      sx={{ py: { xs: 2, md: 1.5 } }}
      className={
        (location.pathname.startsWith(list.link) && list.link !== '/') ||
        (location.pathname === '/' && list.link === '/')
          ? 'active'
          : 'inactive'
      }
    >
      <ListItemIcon className="icon">{!!Icon && <Icon color="action" />}</ListItemIcon>
      <ListItemText
        primaryTypographyProps={{
          fontSize: { xs: '', md: '.8rem' },
          fontFamily: 'Poppins semibold',
        }}
        primary={list.label}
      />
    </ListItemButton>
  );
}

interface SelectedListItemProps {
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

export default function SelectedListItem({ searchQuery = '', onSearchChange }: SelectedListItemProps) {
  const location = useLocation();

  const {
    searchResults,
    isSearching,
    hasResults,
    handleSearchChange,
  } = useMenuSearch({
    menuData: SideBarMenuData,
    debounceDelay: 300,
  });

  // Sync with parent search state
  React.useEffect(() => {
    if (searchQuery !== undefined) {
      handleSearchChange(searchQuery);
    }
  }, [searchQuery, handleSearchChange]);

  // Handle search change from parent
  const handleParentSearchChange = React.useCallback((query: string) => {
    handleSearchChange(query);
    onSearchChange?.(query);
  }, [handleSearchChange, onSearchChange]);

  return (
    <SidebarListRoot>
      <Box sx={{ width: '100%', mt: 1, maxWidth: 360, bgcolor: 'background.paper' }}>
        {isSearching ? (
          <SearchResults
            searchResults={searchResults}
            query={searchQuery}
            isSearching={isSearching}
            hasResults={hasResults}
          />
        ) : (
          <List component="nav">
            {SideBarMenuData.map((list) => {
              return (
                <Link to={`${list.url}`} key={list.id} color="black">
                  <StyledListItem list={list} location={location} />
                </Link>
              );
            })}
          </List>
        )}
      </Box>
    </SidebarListRoot>
  );
}
