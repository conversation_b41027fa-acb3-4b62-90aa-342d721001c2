import Accountancy from '@/assets/timetable/Dashboard/Accountancy.svg';
import Biology from '@/assets/timetable/Dashboard/Biology.svg';
import Chemistry from '@/assets/timetable/Dashboard/Chemistry.svg';
import { TimetableSubjectProps } from '@/types/Timetable';

export const TimetableDatas = [
  { id: '1', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '2', icon: Biology, period: '1st Period', subject: 'Biology', class: 'VII-A', chapter: 'chapter-1' },
  { id: '3', icon: Chemistry, period: '1st Period', subject: 'Chemistry', class: 'VII-A', chapter: 'chapter-1' },
  // { id: '', icon: Physics, period: '1st Period', subject: 'Physics', class: 'VII-A', chapter: 'chapter-1' },
  { id: '4', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '5', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '6', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '7', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '8', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
  { id: '9', icon: Accountancy, period: '1st Period', subject: 'Accountancy', class: 'VII-A', chapter: 'chapter-1' },
];

type TimetableByDayProps = {
  day: string;
  subjects: TimetableSubjectProps[];
};

export const TimetableSubjects: TimetableByDayProps[] = [
  {
    day: 'Mon',
    subjects: [
      {
        id: 1,
        name: 'English',
        teacher: 'Teacher Name',
        icon: 'english',
      },
      {
        id: 2,
        name: 'Malayalam',
        teacher: 'Teacher Name',
        icon: 'malayalam',
      },
      {
        id: 3,
        name: 'Arabic',
        teacher: 'Teacher Name',
        icon: 'arabic',
      },
      {
        id: 4,
        name: 'Hindi',
        teacher: 'Teacher Name',
        icon: 'hindi',
      },
      {
        id: 5,
        name: 'Math',
        teacher: 'Teacher Name',
        icon: 'math',
      },
      {
        id: 6,
        name: 'Biology',
        teacher: 'Teacher Name',
        icon: 'biology',
      },
      {
        id: 7,
        name: 'Physics',
        teacher: 'Teacher Name',
        icon: 'physics',
      },
      {
        id: 8,
        name: 'Social',
        teacher: 'Teacher Name',
        icon: 'social',
      },
    ],
  },
  {
    day: 'Tue',
    subjects: [
      {
        id: 2,
        name: 'Malayalam',
        teacher: 'Teacher Name',
        icon: 'malayalam',
      },
      {
        id: 3,
        name: 'Arabic',
        teacher: 'Teacher Name',
        icon: 'arabic',
      },
      {
        id: 1,
        name: 'English',
        teacher: 'Teacher Name',
        icon: 'english',
      },
      {
        id: 8,
        name: 'Social',
        teacher: 'Teacher Name',
        icon: 'social',
      },
      {
        id: 7,
        name: 'Physics',
        teacher: 'Teacher Name',
        icon: 'physics',
      },
      {
        id: 5,
        name: 'Math',
        teacher: 'Teacher Name',
        icon: 'math',
      },
      {
        id: 6,
        name: 'Biology',
        teacher: 'Teacher Name',
        icon: 'biology',
      },
      {
        id: 4,
        name: 'Hindi',
        teacher: 'Teacher Name',
        icon: 'hindi',
      },
    ],
  },
  {
    day: 'Wed',
    subjects: [
      {
        id: 3,
        name: 'Arabic',
        teacher: 'Teacher Name',
        icon: 'arabic',
      },
      {
        id: 1,
        name: 'English',
        teacher: 'Teacher Name',
        icon: 'english',
      },
      {
        id: 4,
        name: 'Hindi',
        teacher: 'Teacher Name',
        icon: 'hindi',
      },
      {
        id: 6,
        name: 'Biology',
        teacher: 'Teacher Name',
        icon: 'biology',
      },
      {
        id: 2,
        name: 'Malayalam',
        teacher: 'Teacher Name',
        icon: 'malayalam',
      },
      {
        id: 8,
        name: 'Social',
        teacher: 'Teacher Name',
        icon: 'social',
      },
      {
        id: 5,
        name: 'Math',
        teacher: 'Teacher Name',
        icon: 'math',
      },
      {
        id: 7,
        name: 'Physics',
        teacher: 'Teacher Name',
        icon: 'physics',
      },
    ],
  },
  {
    day: 'Thu',
    subjects: [
      {
        id: 4,
        name: 'Hindi',
        teacher: 'Teacher Name',
        icon: 'hindi',
      },
      {
        id: 5,
        name: 'Math',
        teacher: 'Teacher Name',
        icon: 'math',
      },
      {
        id: 6,
        name: 'Biology',
        teacher: 'Teacher Name',
        icon: 'biology',
      },
      {
        id: 7,
        name: 'Physics',
        teacher: 'Teacher Name',
        icon: 'physics',
      },
      {
        id: 1,
        name: 'English',
        teacher: 'Teacher Name',
        icon: 'english',
      },
      {
        id: 3,
        name: 'Arabic',
        teacher: 'Teacher Name',
        icon: 'arabic',
      },
      {
        id: 8,
        name: 'Social',
        teacher: 'Teacher Name',
        icon: 'social',
      },
      {
        id: 2,
        name: 'Malayalam',
        teacher: 'Teacher Name',
        icon: 'malayalam',
      },
    ],
  },
  {
    day: 'Fri',
    subjects: [
      {
        id: 5,
        name: 'Math',
        teacher: 'Teacher Name',
        icon: 'math',
      },
      {
        id: 6,
        name: 'Biology',
        teacher: 'Teacher Name',
        icon: 'biology',
      },
      {
        id: 2,
        name: 'Malayalam',
        teacher: 'Teacher Name',
        icon: 'malayalam',
      },
      {
        id: 8,
        name: 'Social',
        teacher: 'Teacher Name',
        icon: 'social',
      },
      {
        id: 4,
        name: 'Hindi',
        teacher: 'Teacher Name',
        icon: 'hindi',
      },
      {
        id: 7,
        name: 'Physics',
        teacher: 'Teacher Name',
        icon: 'physics',
      },
      {
        id: 1,
        name: 'English',
        teacher: 'Teacher Name',
        icon: 'english',
      },
      {
        id: 3,
        name: 'Arabic',
        teacher: 'Teacher Name',
        icon: 'arabic',
      },
    ],
  },
];
