import React from 'react';
import {
  Box,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Chip,
  Stack,
} from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { TreeIconMap } from '@/config/TreeIconMap';
import { SearchableMenuItem, highlightText } from '@/utils/searchUtils';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import SearchOffIcon from '@mui/icons-material/SearchOff';

const SearchResultsRoot = styled.div`
  a {
    color: inherit;
    text-decoration: none;
  }
  a:hover {
    color: ${(props) => props.theme.palette.primary.main};
  }
  .icon {
    font-size: 18px;
    @media ${breakPointsMaxwidth.md} {
      font-size: 22px;
    }
  }
  .active {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    color: ${(props) => props.theme.palette.primary.main};
    border-right: 2.5px solid;
  }
  .search-result-item {
    border-radius: 8px;
    margin: 4px 8px;
    &:hover {
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.grey[50] : props.theme.palette.grey[800]};
    }
  }
  .breadcrumb {
    font-size: 0.75rem;
    color: ${(props) => props.theme.palette.text.secondary};
    margin-top: 2px;
  }
  .highlighted {
    mark {
      background-color: ${(props) => props.theme.palette.warning.light};
      color: ${(props) => props.theme.palette.warning.contrastText};
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 600;
    }
  }
`;

interface SearchResultItemProps {
  item: SearchableMenuItem;
  query: string;
  location: any;
  onItemClick?: () => void;
}

function SearchResultItem({ item, query, location, onItemClick }: SearchResultItemProps) {
  const Icon = item.icon ? TreeIconMap[item.icon] : null;
  const isActive = location.pathname === item.fullPath;

  const handleClick = () => {
    onItemClick?.();
  };

  return (
    <Link to={item.fullPath} onClick={handleClick}>
      <ListItemButton
        className={`search-result-item ${isActive ? 'active' : ''}`}
        sx={{ py: 1.5, px: 2 }}
      >
        <ListItemIcon className="icon" sx={{ minWidth: 36 }}>
          {!!Icon && <Icon color="action" />}
        </ListItemIcon>
        <ListItemText
          primary={
            <Box>
              <Typography
                variant="body2"
                className="highlighted"
                dangerouslySetInnerHTML={{
                  __html: highlightText(item.label, query),
                }}
                sx={{
                  fontFamily: 'Poppins semibold',
                  fontSize: { xs: '0.875rem', md: '0.8rem' },
                }}
              />
              {item.breadcrumb.length > 1 && (
                <Stack direction="row" spacing={0.5} alignItems="center" className="breadcrumb">
                  {item.breadcrumb.map((crumb, index) => (
                    <React.Fragment key={index}>
                      {index > 0 && <Typography variant="caption">›</Typography>}
                      <Typography
                        variant="caption"
                        className="highlighted"
                        dangerouslySetInnerHTML={{
                          __html: highlightText(crumb, query),
                        }}
                      />
                    </React.Fragment>
                  ))}
                </Stack>
              )}
            </Box>
          }
        />
      </ListItemButton>
    </Link>
  );
}

interface SearchResultsProps {
  searchResults: SearchableMenuItem[];
  query: string;
  isSearching: boolean;
  hasResults: boolean;
  onItemClick?: () => void;
}

export default function SearchResults({
  searchResults,
  query,
  isSearching,
  hasResults,
  onItemClick,
}: SearchResultsProps) {
  const location = useLocation();

  if (!isSearching) {
    return null;
  }

  return (
    <SearchResultsRoot>
      <Box sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}>
        {!hasResults ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 4,
              px: 2,
            }}
          >
            <SearchOffIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              No results found for "{query}"
            </Typography>
            <Typography variant="caption" color="text.secondary" textAlign="center" sx={{ mt: 1 }}>
              Try searching with different keywords
            </Typography>
          </Box>
        ) : (
          <>
            <Box sx={{ px: 2, py: 1 }}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography variant="caption" color="text.secondary">
                  Search Results
                </Typography>
                <Chip
                  label={searchResults.length}
                  size="small"
                  variant="outlined"
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
              </Stack>
            </Box>
            <List component="nav" sx={{ py: 0 }}>
              {searchResults.map((item) => (
                <SearchResultItem
                  key={item.id}
                  item={item}
                  query={query}
                  location={location}
                  onItemClick={onItemClick}
                />
              ))}
            </List>
          </>
        )}
      </Box>
    </SearchResultsRoot>
  );
}
