import { ListMenuItem } from '@/types/Layout';

export interface SearchableMenuItem extends ListMenuItem {
  breadcrumb: string[];
  fullPath: string;
  searchableText: string;
}

export interface RouteDefinition {
  path: string;
  label: string;
  parentPath?: string;
  parentLabel?: string;
}

// Define all sub-routes for each main menu item
export const subRoutes: Record<string, RouteDefinition[]> = {
  '/academic-management': [
    { path: '/academic-management/year', label: 'Manage Year' },
    { path: '/academic-management/class', label: 'Manage Class' },
    { path: '/academic-management/class-section', label: 'Manage Section' },
    { path: '/academic-management/sort-class', label: 'Class Sort' },
    { path: '/academic-management/subject', label: 'Subject' },
    { path: '/academic-management/category-subject', label: 'Subject Category' },
    { path: '/academic-management/language-student', label: 'Language Student' },
    { path: '/academic-management/study-materials', label: 'Study Materials' },
    { path: '/academic-management/events', label: 'Event Details' },
  ],
  '/attendance-marking': [
    { path: '/attendance-marking/class-marking', label: 'Class Marking' },
    { path: '/attendance-marking/detailed-marking', label: 'Detailed Class Marking' },
    { path: '/attendance-marking/session-wise-marking', label: 'Session Wise Marking' },
    { path: '/attendance-marking/quick-marking', label: 'Quick Marking' },
    { path: '/attendance-marking/leave-note', label: 'Leave Note' },
    { path: '/attendance-marking/student-attendance-calendar', label: 'Student Attendance Calendar' },
    { path: '/attendance-marking/attendance-summary', label: 'Attendance Summary' },
    { path: '/attendance-marking/absentees-list', label: 'Absentees List' },
  ],
  '/message-box': [
    { path: '/message-box/quick-send', label: 'Quick Send' },
    { path: '/message-box/quick-send2', label: 'Quick Send 2' },
    { path: '/message-box/sms-template', label: 'SMS Template' },
    { path: '/message-box/send-others', label: 'Send to Others' },
    { path: '/message-box/delivery-report', label: 'Delivery Report' },
  ],
  '/voice-message': [
    { path: '/voice-message/create', label: 'Create Voice Message' },
    { path: '/voice-message/voice-template', label: 'Voice Template' },
  ],
  '/app-notification': [{ path: '/app-notification/create', label: 'Create Notification' }],
  '/manage-students': [
    { path: '/manage-students/new', label: 'New Student' },
    { path: '/manage-students/info', label: 'Student Parent Info' },
    { path: '/manage-students/download-student-info', label: 'Download Student Info' },
    { path: '/manage-students/quick-update', label: 'Quick Update Student' },
    { path: '/manage-students/reallocate', label: 'Class Re-Allocation' },
    { path: '/manage-students/promote', label: 'Promote Students' },
    { path: '/manage-students/remarks', label: 'Students Remarks' },
    { path: '/manage-students/extracurricular', label: 'Students Extracurricular' },
  ],
  '/staff-management': [
    { path: '/staff-management/list', label: 'Manage Staffs' },
    { path: '/staff-management/allocation-list', label: 'CTS Allocation' },
    { path: '/staff-management/class-wise-allocation', label: 'Class Wise Allocation' },
    { path: '/staff-management/teacher-wise-allocation', label: 'Teacher Wise Allocation' },
    { path: '/staff-management/re-allocate', label: 'Staff Re-Allocation' },
    { path: '/staff-management/hours-list', label: 'Hours List' },
    { path: '/staff-management/staff-activity-report', label: 'Staff Activity Report' },
    { path: '/staff-management/category-list', label: 'Staff Category' },
    { path: '/staff-management/category-map', label: 'Staff Category Map' },
    { path: '/staff-management/conveyors', label: 'Conveyors' },
  ],
  '/staff-attendance': [
    { path: '/staff-attendance/leave-list', label: 'Leave List' },
    { path: '/staff-attendance/working-day-list', label: 'Staff Working Day List' },
    { path: '/staff-attendance/punch-time-set', label: 'Staff Punch Time Set' },
    { path: '/staff-attendance/punch-list', label: 'Punch List' },
    { path: '/staff-attendance/punch-report', label: 'Punch Report' },
  ],
  '/manage-fee': [
    { path: '/manage-fee/overview', label: 'Overview' },
    { path: '/manage-fee/basic-fee-list', label: 'Basic Fee List' },
    { path: '/manage-fee/term-fee-list', label: 'Term Fee List' },
    { path: '/manage-fee/collection', label: 'Fee Collection' },
    { path: '/manage-fee/pay-fee', label: 'Pay Fee' },
    { path: '/manage-fee/pay-fee-details', label: 'Pay Fee Details' },
    { path: '/manage-fee/total-paid-list', label: 'Total Paid List' },
    { path: '/manage-fee/basicfee-paid-list', label: 'Basic Fee Paid List' },
    { path: '/manage-fee/termfee-paid-list', label: 'Term Fee Paid List' },
    { path: '/manage-fee/daily-report', label: 'Daily Report' },
    { path: '/manage-fee/total-pending-list', label: 'Total Pending List' },
    { path: '/manage-fee/total-pending-basic-list', label: 'Basic Fee Pending List' },
    { path: '/manage-fee/total-pending-term-list', label: 'Term Fee Pending List' },
    { path: '/manage-fee/fee-setting', label: 'Fee Setting' },
    { path: '/manage-fee/fine-setting', label: 'Fine Setting' },
    { path: '/manage-fee/optional-fee', label: 'Optional Fee' },
    { path: '/manage-fee/fees-details', label: 'Fees Details' },
    { path: '/manage-fee/fee-date-settings', label: 'Fee Date Settings' },
    { path: '/manage-fee/optional-fee-setting', label: 'Optional Fee Setting' },
  ],
  '/manage-bus-fees': [
    { path: '/manage-bus-fees/bus-list', label: 'Bus List' },
    { path: '/manage-bus-fees/route-list', label: 'Route List' },
    { path: '/manage-bus-fees/stop-list', label: 'Stop List' },
    { path: '/manage-bus-fees/fee-collection', label: 'Bus Fee Collection' },
    { path: '/manage-bus-fees/paid-list', label: 'Bus Fee Paid List' },
    { path: '/manage-bus-fees/pending-list', label: 'Bus Fee Pending List' },
    { path: '/manage-bus-fees/daily-report', label: 'Bus Fee Daily Report' },
  ],
  '/library-management': [
    { path: '/library-management/new-book', label: 'Enroll Books' },
    { path: '/library-management/book-list', label: 'Book List' },
    { path: '/library-management/issue-list', label: 'Issue List' },
    { path: '/library-management/category', label: 'Category Manager' },
    { path: '/library-management/location', label: 'Location Manager' },
    { path: '/library-management/fine-setting', label: 'Book Fine Setting' },
  ],
  '/store-management': [
    { path: '/store-management/product-list', label: 'Manage Products' },
    { path: '/store-management/map-product', label: 'Map Products' },
    { path: '/store-management/mapped-list', label: 'Mapped List' },
    { path: '/store-management/new-sale', label: 'Product Sale' },
    { path: '/store-management/sale-list', label: 'Sale List' },
    { path: '/store-management/daily-report', label: 'Store Daily Report' },
  ],
  '/assignment': [{ path: '/assignment/assignment-list', label: 'Assignment List' }],
  '/online-exam': [{ path: '/online-exam/exam-details', label: 'Exam Details' }],
  '/live-class': [{ path: '/live-class/schedule-list', label: 'Schedule List' }],
  '/online-video-class': [{ path: '/online-video-class/student-device-details', label: 'Student Device Details' }],
  '/report': [
    { path: '/report/student-details', label: 'Student Details' },
    { path: '/report/student-birthday-list', label: 'Student Birthday List' },
    { path: '/report/attendance-report', label: 'Attendance Report' },
    { path: '/report/absentees-list', label: 'Absentees List' },
    { path: '/report/teacher-subject-list', label: 'Teacher Subject List' },
    { path: '/report/class-teacher-info', label: 'Class Teacher Info' },
    { path: '/report/pta-members-list', label: 'PTA Members List' },
    { path: '/report/staff-list', label: 'Staff List' },
    { path: '/report/conveyors-list', label: 'Conveyors List' },
  ],
  '/pta-forum': [{ path: '/pta-forum/members-list', label: 'Members List' }],
  '/admin-controls': [{ path: '/admin-controls/send-updates', label: 'Send Updates' }],
  '/general-settings': [{ path: '/general-settings/about-us', label: 'About Us' }],
  '/group-settings': [{ path: '/group-settings/groups', label: 'Groups' }],
  '/school-calendar': [{ path: '/school-calendar/calendar', label: 'Calendar' }],
  '/certificate': [{ path: '/certificates/TC-list', label: 'TC List' }],
  '/tc-cc': [{ path: '/tc-cc/tc-list', label: 'TC List' }],
  '/school-plan': [
    { path: '/school-plan/session-plan-list', label: 'Session Plan List' },
    { path: '/school-plan/lesson-plan-list', label: 'Lesson Plan List' },
    { path: '/school-plan/annual-plan-list', label: 'Annual Plan List' },
  ],
  '/album': [
    { path: '/album/full-list', label: 'Full List' },
    { path: '/album/photo-list', label: 'Photo List' },
    { path: '/album/video-list', label: 'Video List' },
  ],
  '/payments': [{ path: '/payments/category', label: 'Payment Category' }],
};

/**
 * Creates searchable menu items from the main menu data and sub-routes
 */
export function createSearchableMenuItems(menuData: ListMenuItem[]): SearchableMenuItem[] {
  const searchableItems: SearchableMenuItem[] = [];

  // Add main menu items
  menuData.forEach((item) => {
    const searchableItem: SearchableMenuItem = {
      ...item,
      breadcrumb: [item.label],
      fullPath: item.url || item.link || '',
      searchableText: item.label.toLowerCase(),
    };
    searchableItems.push(searchableItem);

    // Add sub-routes for this main menu item
    const itemSubRoutes = subRoutes[item.link || ''];
    if (itemSubRoutes) {
      itemSubRoutes.forEach((subRoute) => {
        const subItem: SearchableMenuItem = {
          id: `${item.id}-${subRoute.path.split('/').pop()}`,
          label: subRoute.label,
          icon: item.icon,
          link: subRoute.path,
          url: subRoute.path,
          breadcrumb: [item.label, subRoute.label],
          fullPath: subRoute.path,
          searchableText: `${item.label} ${subRoute.label}`.toLowerCase(),
        };
        searchableItems.push(subItem);
      });
    }
  });

  return searchableItems;
}

/**
 * Filters searchable menu items based on search query
 */
export function filterMenuItems(searchableItems: SearchableMenuItem[], query: string): SearchableMenuItem[] {
  if (!query.trim()) {
    return [];
  }

  const normalizedQuery = query.toLowerCase().trim();

  return searchableItems.filter((item) => {
    return item.searchableText.includes(normalizedQuery);
  });
}

/**
 * Highlights matching text in a string
 */
export function highlightText(text: string, query: string): string {
  if (!query.trim()) {
    return text;
  }

  const normalizedQuery = query.toLowerCase().trim();
  const regex = new RegExp(`(${normalizedQuery})`, 'gi');

  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}
