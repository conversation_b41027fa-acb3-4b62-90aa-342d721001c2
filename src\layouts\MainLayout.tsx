import SideBar from '@/features/mainlayout/SideBar/SideBar';
import TopBar from '@/features/mainlayout/TopBar';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import { Link, Outlet } from 'react-router-dom';
import styled, { useTheme } from 'styled-components';
import { breakPointsMinwidth } from '@/config/breakpoints';
// import TreeMenu from '@/features/mainlayout/SideBar/TreeMenu';
// import { TreeMenuItem } from '@/types/Layout';
import { AnimatePresence } from 'framer-motion';
import SelectedListItem from '@/features/mainlayout/SideBar/ListView';
import Switch from '@mui/material/Switch';
import { useEffect, useRef, useState } from 'react';
import { Stack, Typography, ToggleButtonGroup, ToggleButton } from '@mui/material';
import ParentSelectedListItem from '@/features/mainlayout/SideBar/ParentListView';
import useAuth from '@/hooks/useAuth';

const Content = styled.section<{ $topBarHeight: number }>`
  padding-top: ${({ $topBarHeight }) => `${$topBarHeight}px`};
  @media ${breakPointsMinwidth.md} {
    padding-left: ${SIDE_BAR_WIDTH};
  }
  overflow-x: hidden;
`;

function MainLayout() {
  const topBarRef = useRef<HTMLElement>(null);
  const [topBarHeight, setTopBarHeight] = useState(0);
  const { parentMode } = useAuth();

  useEffect(() => {
    if (topBarRef.current) {
      setTopBarHeight(topBarRef.current.offsetHeight);
    }
    const handleResize = () => {
      if (topBarRef.current) {
        setTopBarHeight(topBarRef.current.offsetHeight);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // const handleMenuItemClick = useCallback(
  //   (list: ListMenuItem) => {
  //     console.log(list);
  //     if (list && list.link && !window.matchMedia(breakPointsMinwidth.md).matches) {
  //       dispatch(toggleSidebar());
  //     }
  //   },
  //   [dispatch]
  // );
  // const [adminView, setAdminView] = useState(true);
  console.log('Parent Mode:', parentMode);
  return (
    <main className="passdaily-mainLayout-root">
      <TopBar ref={topBarRef} />
      <SideBar topBarHeight={topBarHeight}>
        {/* 
        <Stack direction="row" justifyContent="center" width="100%">
          <Typography pt={1.5} variant="subtitle2">
            Admin
          </Typography>
          <Switch
            checked={adminView}
            onChange={() => setAdminView(!adminView)}
            LinkComponent={Link}
            to="/attendance-marking/quick-marking"
          />
          <Typography pt={1.5} variant="subtitle2">
            Parent
          </Typography>
          <ToggleButtonGroup
            value={!adminView}
            exclusive
            onChange={() => setAdminView(!adminView)}
            aria-label="Platform"
            size="small"
          >
            <ToggleButton value={false} sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
              <Link to="/">
                <Typography variant="h6" color={adminView ? 'inherit' : theme.palette.grey[500]}>
                  ADMIN
                </Typography>
              </Link>
            </ToggleButton>
            <ToggleButton value={true} sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
              <Link to="/parent">
                <Typography variant="h6" color={adminView ? theme.palette.grey[500] : 'inherit'}>
                  PARENT
                </Typography>
              </Link>
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
                */}
        {/* <List sx={{ paddingTop: '8px', paddingBottom: 0 }}>
          <ListItem disablePadding component={Link} to="/" onClick={handleMenuItemClick}>
            <ListItemButton>
              <ListItemIcon>
                <GridViewIcon />
              </ListItemIcon>
              <ListItemText primary="Dashboard" />
            </ListItemButton>
          </ListItem>
        </List>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel1a-content" id="panel1a-header1">
            <Typography>
              <HowToRegIcon sx={{ mr: 1 }} /> Attendance Marking
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List sx={{ paddingTop: 0, paddingBottom: 0 }}>
              <ListItem disablePadding component={Link} to="/classmarking" onClick={handleMenuItemClick}>
                <ListItemButton>
                  <ListItemText primary="Class Marking" />
                </ListItemButton>
              </ListItem>
              <Divider />
              <ListItem disablePadding component={Link} to="/classmarking-detail" onClick={handleMenuItemClick}>
                <ListItemButton>
                  <ListItemText primary="Detail Class Marking" />
                </ListItemButton>
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion> */}
        {!parentMode ? <SelectedListItem /> : <ParentSelectedListItem />}
      </SideBar>
      <Content $topBarHeight={topBarHeight}>
        <AnimatePresence mode="wait">
          <Outlet context={{ topBarHeight }} />
        </AnimatePresence>
      </Content>
    </main>
  );
}

export default MainLayout;
