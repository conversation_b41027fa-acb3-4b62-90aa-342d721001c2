import { breakPointsMinwidth } from '@/config/breakpoints';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getSideBarState, toggleSidebar } from '@/store/Layout/layout.slice';
import { Drawer, Typography, Divider, Box, Stack, TextField, InputAdornment } from '@mui/material';
import { forwardRef, ReactNode, useState, useMemo } from 'react';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import IconButton from '@mui/material/IconButton';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
// import passdailyLogo from '@/assets/SchoolLogos/logo-small.svg';
// import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
// import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
// import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
// import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
// import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
// import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';

const SideBarRoot = styled.aside`
  position: fixed;
  top: ${TOP_BAR_HEIGHT};
  left: 0;
  width: 0;
  height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  box-shadow: ${(props) =>
    props.theme.themeMode === 'light' ? '0 1px 2px 0 rgb(0 0 0 / 5%)' : '0px 3px 9px #00000066'};
  border-right: ${(props) =>
    props.theme.themeMode === 'light' ? '1px solid rgb(185 182 182 / 18%)' : '1px solid rgb(0 0 0 / 18%)'};

  z-index: ${(props) => props.theme.zIndex.appBar - 10};

  @media ${breakPointsMinwidth.md} {
    width: ${SIDE_BAR_WIDTH};
  }
  a {
    text-decoration: none;
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[900] : props.theme.palette.white};
  }
`;

export type SideBarProps = {
  window?: () => Window;
  children: ReactNode;
  topBarHeight?: any;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
};

const SideBar = forwardRef<HTMLElement, SideBarProps>((props) => {
  const { window, children, topBarHeight, searchQuery = '', onSearchChange } = props;

  const sideBarOpen = useAppSelector(getSideBarState);
  const dispatch = useAppDispatch();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  const container = window !== undefined ? () => window().document.body : undefined;

  const handleDrawerToggle = () => {
    dispatch(toggleSidebar());
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setLocalSearchQuery(query);
    onSearchChange?.(query);
  };

  const handleClearSearch = () => {
    setLocalSearchQuery('');
    onSearchChange?.('');
  };
  return (
    <SideBarRoot>
      <Drawer
        container={container}
        variant="temporary"
        open={sideBarOpen}
        onClick={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: '18rem',
            paddingTop: '15px',
            '&::-webkit-scrollbar': {
              width: '0px',
            },
          },
        }}
      >
        <Stack className="brand-wrap" sx={{ height: '3rem', pl: '20px' }} direction="row" alignItems="center">
          <Box
            className="logo-icon me-2 rounded-circle"
            sx={{ width: '45px', height: '45px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            border="1px solid #f1f2f3"
          >
            {/* <img src={passdailyLogo} alt="passdailLogo" width="45px" height="45px" /> */}
            <img src={holyLogo} alt="holyLogo" width="45px" height="45px" />
            {/* <img src={carmelLogo} alt="carmelLogo" width="45px" height="45px" /> */}
            {/* <img src={thereseLogo} alt="thereseLogo" width="45px" height="45px" /> */}
            {/* <img src={thomasLogo} alt="thomasLogo" width="45px" height="45px" /> */}
            {/* <img src={nirmalaLogo} alt="nirmalaLogo" width="45px" height="45px" /> */}
            {/* <img src={MIMLogo} alt="MIMLogo" width="45px" height="45px" /> */}
          </Box>
          <Typography fontWeight={600} fontSize={20}>
            {/* Passdaily */}
            Holy Angels
            {/* Carmel */}
            {/* St Therese */}
            {/* St Thomas */}
            {/* Nirmala */}
            {/* MIM School */}
          </Typography>
        </Stack>

        <Divider color="gray" className="my-2" />

        {/* Search Input */}
        <Box sx={{ px: 2, pb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search navigation..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: localSearchQuery && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch} edge="end">
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                fontSize: '0.875rem',
                '& fieldset': {
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(0, 0, 0, 0.87)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
          />
        </Box>

        {children}
      </Drawer>
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            // width: ` calc(1rem + ${SIDE_BAR_WIDTH}) `,
            width: SIDE_BAR_WIDTH,
            marginTop: `${topBarHeight}px`,
            height: `calc(100% - ${TOP_BAR_HEIGHT})`,
            py: '15px',
            '&::-webkit-scrollbar': {
              width: 0,
            },
          },
        }}
        open
      >
        {/* Search Input for Desktop */}
        <Box sx={{ px: 2, pb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search..."
            value={localSearchQuery}
            // variant="standard"
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: localSearchQuery && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch} edge="end">
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                fontSize: '0.875rem',
                '& fieldset': {
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(0, 0, 0, 0.87)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
          />
        </Box>

        {children}
      </Drawer>
    </SideBarRoot>
  );
});

export default SideBar;
