/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import { SIDE_BAR_WIDTH, TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Avatar,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  useTheme,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  useMediaQuery,
  Chip,
  Select,
  MenuItem,
  TablePagination,
  SelectChangeEvent,
  Alert,
} from '@mui/material';
import styled from 'styled-components';
import { GENDER_SELECT } from '@/config/Selection';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import Success from '@/assets/ManageFee/Success.json';
import man from '@/assets/man.png';
import woman from '@/assets/woman.png';
import errorFailedIcon from '@/assets/ManageFee/ErrorIcon.json';
import NoData from '@/assets/no-datas.png';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import { BsGenderAmbiguous } from 'react-icons/bs';
import { BiTable } from 'react-icons/bi';
import { TiBusinessCard } from 'react-icons/ti';
import { MdAdd } from 'react-icons/md';
import { Student, StudentInfoData } from '@/config/StudentDetails';
import MenuEditDelete from '@/components/shared/Selections/MenuEditDelete';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { bluePreset } from '@/utils/Colors';
import DateSelect from '@/components/shared/Selections/DateSelect';
import { StudentListInfo, StudentListRequest } from '@/types/StudentManagement';
import { YearDataType } from '@/types/Dashboard';
import {
  addNewStudent,
  fetchStudentList,
  updateStudent,
  deleteStudent,
} from '@/store/Students/studentManagement.thunks';
import {
  getStudentListData,
  getStudentListPageInfo,
  getStudentListStatus,
  getStudentDeletingRecords,
  getStudentSortColumn,
  getStudentSortDirection,
  getStudentSubmitting,
  getClassData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import useAuth from '@/hooks/useAuth';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { setSortColumn, setSortDirection } from '@/store/Students/studentManagement.slice';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ClassListInfo } from '@/types/AcademicManagement';
import dayjs, { Dayjs } from 'dayjs';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import useSettings from '@/hooks/useSettings';
import SortIcon from '@mui/icons-material/Sort';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';
import TransgenderIcon from '@mui/icons-material/Transgender';
import { useNavigate } from 'react-router-dom';
import LocalPhoneRoundedIcon from '@mui/icons-material/LocalPhoneRounded';
import StudentAddIndividual from './StudentAddIndividual';
import { ToggleButtonGroup } from '@mui/material';
import { ToggleButton } from '@mui/material';
import BackButton from '@/components/shared/BackButton';
import { AddStudentMultipleProps } from './AddStudentMultiple';
import * as XLSX from 'xlsx';
// import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import GridOnIcon from '@mui/icons-material/GridOn';
import { Snackbar } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check'; // Make sure to import the icon you want
import { SuccessIcon } from '@/theme/overrides/CustomIcons';

const DownloadStudentInfoRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;


  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    /* min-height: calc(100vh - 1160px); */
    @media screen and (max-width: 768px) {
      margin-bottom: 6px;
    }
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      /* max-height: calc(100% - 40px); */
      flex-grow: 1;
      .card-table-container {
        flex-grow: 1;
        width: 100%;
        /* height: calc(100vh - 255px); */
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
      .main_student_card {
        padding-bottom: 30px;
        height: calc(100vh - 285px);
        overflow-y: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }
        @media screen and (max-width: 768px) {
          /* height: 100%; */
          height: calc(100vh - 290px);
        }
      }
    }

    @media screen and (max-width: 991px) {
      .student_table {
        display: none;
      }
    }
    @media screen and (max-width: 991px) {
      .change-view-button {
        display: none;
      }
    }
  }
`;

const DefaultStudentInfo: StudentListInfo = {
  studentId: 0,
  academicId: 0,
  classId: 0,
  studentName: '',
  admissionNumber: '',
  admissionDate: '',
  studentGender: -2,
  studentDOB: '',
  studentLastStudied: '',
  studentBloodGroup: '',
  studentBirthPlace: '',
  studentNationality: '',
  studentMotherTongue: '',
  studentReligion: '',
  studentCaste: '',
  studentPAddress: '',
  studentCAddress: '',
  studentFatherName: '',
  studentFatherQualification: '',
  studentFatherOccupation: '',
  studentFatherNumber: '',
  studentEmailId: '',
  studentMotherName: '',
  studentMotherQualification: '',
  studentMotherOccupation: '',
  studentMotherNumber: '',
  studentImage: '',
  createdBy: 0,
};

function DownloadStudentInfo({ onBackClick }: AddStudentMultipleProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const adminId: number = user ? user.accountId : 0;
  const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(true);
  const { confirm } = useConfirm();

  // const [forceDispatch, setForceDispatch] = useState(false);
  const [selectedStudentDetail, setSelectedStudentDetail] = useState<StudentListInfo>(DefaultStudentInfo);
  const ClassData = useAppSelector(getClassData);
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };
  const AllYearOption: YearDataType = {
    accademicId: 0,
    accademicTime: 'Select Year',
    accademicStatus: 1,
  };
  const AllGenderOption = {
    id: '-1',
    gender: 'All',
  };
  const defualtYear = YearData[0]?.accademicId || 0;
  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const yearDataWithAllYear = [AllYearOption, ...YearData];
  const gendersDataWithAll = [AllGenderOption, ...GENDER_SELECT];
  const [classFilter, setClassFilter] = useState(classDataWithAllClass[0]);
  const [studentNameFilter, setStudentNameFilter] = useState('');
  const [studentGenderFilter, setStudentGenderFilter] = useState(-1);
  const [admissionNumberFilter, setAdmissionNumberFilter] = useState('');
  const [academicYearFilter, setAcademicYearFilter] = useState(defualtYear);
  const [studentFatherNameFilter, setStudentFatherNameFilter] = useState('');
  const [studentFatherNumberFilter, setStudentFatherNumberFilter] = useState('');
  // const [studentBloodGroupFilter, setStudentBloodGroupFilter] = useState('');
  // const [studentCasteFilter, setStudentCasteFilter] = useState('');
  const [showAddStudents, setShowAddStudents] = React.useState<
    'individual' | 'multiple' | 'studentInfo' | 'downloadInfo'
  >('studentInfo');
  const [selectedFields, setSelectedFields] = useState([]); // Default columns
  const StudentListStatus = useAppSelector(getStudentListStatus);
  const StudentListData = useAppSelector(getStudentListData);
  // const StudentListError = useAppSelector(getStudentListError);
  const paginationInfo = useAppSelector(getStudentListPageInfo);
  const sortColumn = useAppSelector(getStudentSortColumn);
  const sortDirection = useAppSelector(getStudentSortDirection);
  const isSubmitting = useAppSelector(getStudentSubmitting);
  const deletingRecords = useAppSelector(getStudentDeletingRecords);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { pagenumber, pagesize, totalrecords } = paginationInfo;
  const { classId, className } = classFilter || {};
  // const { gender } = studentGenderFilter || {};

  const currentStudentListRequest = useMemo(
    () => ({
      pageNumber: pagenumber,
      pageSize: pagesize,
      sortColumn,
      sortDirection,
      filters: {
        adminId,
        academicId: academicYearFilter,
        classId,
        studentName: studentNameFilter,
        studentGender: studentGenderFilter,
        admissionDate: '',
        admissionNumber: admissionNumberFilter,
        studentDob: '',
        studentFatherName: studentFatherNameFilter,
        studentFatherNumber: studentFatherNumberFilter,
        studentBloodGroup: '',
        studentCaste: '',
      },
    }),
    [
      adminId,
      pagenumber,
      pagesize,
      sortColumn,
      sortDirection,
      classId,
      academicYearFilter,
      studentNameFilter,
      studentGenderFilter,
      admissionNumberFilter,
      studentFatherNameFilter,
      studentFatherNumberFilter,
    ]
  );

  const loadStudentList = useCallback(
    (request: StudentListRequest) => {
      dispatch(fetchStudentList(request));
    },
    [dispatch]
  );

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === e.target.value);
    if (selectedClass) {
      setClassFilter(selectedClass);
    }
    loadStudentList({
      ...currentStudentListRequest,
      pageNumber: 1,
      filters: { ...currentStudentListRequest.filters, classId: selectedClass ? selectedClass.classId : 0 },
    });
  };
  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(e.target.value, 10));
    loadStudentList({
      ...currentStudentListRequest,
      filters: { ...currentStudentListRequest.filters, academicId: parseInt(e.target.value, 10) },
    });
  };
  const handleGenderChange = (e: SelectChangeEvent) => {
    setStudentGenderFilter(parseInt(GENDER_SELECT.filter((item) => item.id === e.target.value)[0].id, 10));
    loadStudentList({
      ...currentStudentListRequest,
      filters: { ...currentStudentListRequest.filters, studentGender: parseInt(e.target.value, 10) },
    });
  };

  const handleAddNewStudent = () => {
    setSelectedStudentDetail(DefaultStudentInfo);
    setShowAddStudents('individual');
  };

  const handleSaveorEdit = useCallback(
    async (value: StudentListInfo, mode: 'create' | 'edit') => {
      if (mode === 'create') {
        const { studentId, ...rest } = value;
        const response = await dispatch(addNewStudent(rest)).unwrap();

        if (response.id > 0) {
          setShowAddStudents('studentInfo');
          const successMessage = <SuccessMessage message="Student created successfully" />;
          await confirm(successMessage, 'Student Created', { okLabel: 'Ok', showOnlyOk: true });

          loadStudentList({
            ...currentStudentListRequest,
            pageNumber: 1,
            sortColumn: 'studentId',
            sortDirection: 'desc',
          });
        }
      } else {
        const { studentDOB, admissionDate } = value;

        const isDateFormatted = (date: string) => {
          const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
          return dateRegex.test(date);
        };

        // Function to format dates
        const formatDate = (date: Dayjs | string | number | null): string | null => {
          if (date === null) return null;
          if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
          if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
          if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
          return null; // Fallback for any unexpected types
        };

        // Convert startDate and endDate to DD/MM/YYYY format
        const formattedStudentDOB = formatDate(studentDOB);
        const formattedStudentJoinDate = formatDate(admissionDate);
        const updateReq = {
          ...value,
          studentDOB: formattedStudentDOB,
          admissionDate: formattedStudentJoinDate,
          dbResult: '',
        };
        const response = await dispatch(updateStudent(updateReq)).unwrap();
        if (response.rowsAffected > 0) {
          const successMessage = <SuccessMessage message="Student updated successfully" />;
          await confirm(successMessage, 'Student Updated', { okLabel: 'Ok', showOnlyOk: true });

          loadStudentList(currentStudentListRequest);
        }
      }
    },
    [confirm, currentStudentListRequest, dispatch, loadStudentList]
  );

  useEffect(() => {
    if (YearStatus === 'idle') {
      setAcademicYearFilter(defualtYear);
    }
    if (StudentListStatus === 'idle') {
      loadStudentList(currentStudentListRequest);
      dispatch(fetchYearList(adminId));
      dispatch(fetchClassList(adminId));
    }
    console.log('datass::', StudentListData);
  }, [
    loadStudentList,
    defualtYear,
    YearStatus,
    StudentListStatus,
    currentStudentListRequest,
    StudentListData,
    adminId,
    dispatch,
  ]);

  const getRowKey = useCallback((row: StudentListInfo) => row.studentId, []);

  const handlePageChange = useCallback(
    (event: unknown, newPage: number) => {
      loadStudentList({ ...currentStudentListRequest, pageNumber: newPage + 1 });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = +event.target.value;
      loadStudentList({ ...currentStudentListRequest, pageNumber: 1, pageSize: newPageSize });
    },
    [currentStudentListRequest, loadStudentList]
  );

  const handleEditStudent = useCallback((studentObj: StudentListInfo) => {
    setSelectedStudentDetail(studentObj);
    console.log(studentObj);
    setShowAddStudents('individual');
  }, []);

  const currentItemCount = StudentListData.length;

  const handleDeleteStudent = useCallback(
    async (studentObj: StudentListInfo) => {
      try {
        const { studentId, studentName } = studentObj;
        const deleteConfirmMessage = (
          <DeleteMessage
            jsonIcon={deleteBin}
            message={
              <div>
                Are you sure you want to delete the Student <br />
                <span style={{ color: theme.palette.error.main }}>&quot;{studentName}&quot;</span> ?
              </div>
            }
          />
        );
        if (await confirm(deleteConfirmMessage, 'Delete Student?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
          const deleteResponse = await dispatch(deleteStudent(studentId)).unwrap();
          if (deleteResponse.deleted) {
            const deleteDoneMessage = (
              <DeleteMessage loop={false} jsonIcon={deleteSuccess} message="Student deleted successfully." />
            );
            await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
            let pageNumberToMove = pagenumber;
            if (paginationInfo.remainingpages === 0 && pagenumber > 1 && currentItemCount === 1) {
              pageNumberToMove = pagenumber - 1;
              loadStudentList({ ...currentStudentListRequest, pageNumber: pageNumberToMove });
            } else {
              loadStudentList(currentStudentListRequest);
            }
          } else {
            const errorMessage = (
              <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Student deleted failed" />
            );
            await confirm(errorMessage, 'Student Delete', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={errorFailedIcon} message="Something went wrong please try again." />
        );
        await confirm(errorMessage, 'Student Delete', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [
      confirm,
      currentStudentListRequest,
      currentItemCount,
      dispatch,
      loadStudentList,
      pagenumber,
      paginationInfo.remainingpages,
      theme,
    ]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newReq = { ...currentStudentListRequest };
      if (column === sortColumn) {
        console.log('Toggling direction');
        const sortDirectionNew = sortDirection === 'asc' ? 'desc' : 'asc';
        newReq.pageNumber = 1;
        newReq.sortDirection = sortDirectionNew;
        dispatch(setSortDirection(sortDirectionNew));
      } else {
        newReq.pageNumber = 1;
        newReq.sortColumn = column;
        dispatch(setSortColumn(column));
      }

      loadStudentList(newReq);
    },
    [currentStudentListRequest, dispatch, loadStudentList, sortColumn, sortDirection]
  );

  const lgScreenSize = useMediaQuery(theme.breakpoints.down('lg'));
  React.useEffect(() => {
    if (lgScreenSize) {
      setChangeView(true);
    } else {
      setChangeView(false);
    }
  }, [lgScreenSize]);

  const handleToggle = () => {
    setChangeView((prevChangeView) => !prevChangeView);
    setSelectedStudentDetail(DefaultStudentInfo);
  };

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: pagenumber - 1,
      pageSize: pagesize,
      totalRecords: totalrecords,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, pagenumber, pagesize, totalrecords]
  );

  const handleReset = useCallback(
    (e: any) => {
      e.preventDefault();
      setClassFilter(classDataWithAllClass[0]);
      setAcademicYearFilter(defualtYear);
      setStudentNameFilter('');
      setStudentNameFilter('');
      setStudentGenderFilter(-1);
      setAdmissionNumberFilter('');
      setStudentFatherNameFilter('');
      setStudentFatherNumberFilter('');
      loadStudentList({
        pageNumber: 1,
        pageSize: 20,
        sortColumn: 'studentId',
        sortDirection: 'asc',
        filters: {
          adminId,
          academicId: defualtYear,
          classId: -1,
          studentName: '',
          studentGender: -1,
          admissionDate: '',
          admissionNumber: '',
          studentDob: '',
          studentFatherName: '',
          studentFatherNumber: '',
          studentBloodGroup: '',
          studentCaste: '',
        },
      });
    },
    [loadStudentList, classDataWithAllClass, defualtYear, adminId]
  );
  const StudentsParentInfoColumns: DataTableColumn<any>[] = useMemo(
    () => [
      {
        name: 'admissionNumber',
        dataKey: 'admissionNumber',
        headerLabel: 'Admission No',
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={1} alignItems="center">
              <Avatar alt="" src={row.studentImage} />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'class',
        dataKey: 'className',
        headerLabel: 'Class',
      },
      {
        name: 'academicYear',
        dataKey: 'academicTime',
        headerLabel: 'Year',
      },
      {
        name: 'gender',
        dataKey: 'studentGender',
        headerLabel: 'Gender',
        renderCell: (row) => {
          return (
            <Typography variant="subtitle1" fontSize={14}>
              {row.studentGender === 0 ? 'Male' : row.studentGender === 1 ? 'Female' : 'Other'}
            </Typography>
          );
        },
      },
      {
        name: 'studentFatherName',
        dataKey: 'studentFatherName',
        headerLabel: 'Parent',
      },
      {
        name: 'studentFatherNumber',
        dataKey: 'studentFatherNumber',
        headerLabel: 'Phone Number',
      },
    ],
    [handleEditStudent, handleDeleteStudent]
  );

  const handleFieldToggle = (event: React.MouseEvent<HTMLElement>, newFields: string[]) => {
    if (newFields.length) {
      setSelectedFields(newFields);
    }
  };

  const filteredColumns = useMemo(
    () => StudentsParentInfoColumns.filter((col) => selectedFields.includes(col.name)),
    [StudentsParentInfoColumns, selectedFields]
  );

  // const handleExportExcel = () => {
  //   const exportData = StudentListData.map((student) => ({
  //     Name: student.studentName,
  //     'Admission Number': student.admissionNumber,
  //     Class: student.className,
  //     'Academic Year': student.academicTime,
  //     Gender: student.studentGender === 0 ? 'Male' : student.studentGender === 1 ? 'Female' : 'Other',
  //     'Blood Group': student.studentBloodGroup,
  //     'D.O.B': dayjs(student.studentDOB).format('DD/MM/YYYY'),
  //     Parent: student.studentFatherName,
  //     Phone: student.studentMobile,
  //   }));

  //   const worksheet = XLSX.utils.json_to_sheet(exportData);
  //   const workbook = XLSX.utils.book_new();
  //   XLSX.utils.book_append_sheet(workbook, worksheet, 'StudentInfo');

  //   const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  //   const data = new Blob([excelBuffer], { type: 'application/octet-stream' });
  //   saveAs(data, 'Student_Info.xlsx');
  // };

  const handleExportPDF = async () => {
    const input = document.querySelector('.student_card') as HTMLElement;

    if (!input) {
      setSnackbarMessage('Export area not found!');
      setSnackbarOpen(true);
      return;
    }

    const canvas = await html2canvas(input, { scale: 2 });
    const imgData = canvas.toDataURL('image/png');

    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    const margin = 10; // in mm

    const availableWidth = pdfWidth - margin * 2;
    const availableHeight = pdfHeight - margin * 2;

    const imgProps = pdf.getImageProperties(imgData);
    const imgRatio = imgProps.width / imgProps.height;
    const availableRatio = availableWidth / availableHeight;

    let imgWidth = availableWidth;
    let imgHeight = availableHeight;

    // Adjust image size to preserve aspect ratio
    if (imgRatio > availableRatio) {
      imgHeight = availableWidth / imgRatio;
    } else {
      imgWidth = availableHeight * imgRatio;
    }

    const x = (pdfWidth - imgWidth) / 2;
    const y = (pdfHeight - imgHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);
    pdf.save('Student_Info.pdf');
  };

  return (
    <Page title="Student Info">
      <DownloadStudentInfoRoot>
        <Card className="Card" elevation={1}>
          <Stack
            direction="row"
            flexWrap="wrap"
            justifyContent="space-between"
            alignItems="center"
            sx={{ px: { xs: 3, md: 5 }, pt: { xs: 2, md: 3 }, pb: 1 }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" flex={1} whiteSpace="nowrap">
              <Stack direction="row" alignItems="center">
                <BackButton
                  onBackClick={() => {
                    onBackClick();
                    navigate('info');
                  }}
                />
                <Typography variant="h6" fontSize={17}>
                  Download Student Info
                </Typography>
              </Stack>
            </Stack>
            <div>
              <Tooltip title="Search">
                <IconButton aria-label="search" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
            </div>
          </Stack>
          <Divider />

          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2} sx={{ px: { xs: 3, md: 5 } }}>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 200 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={className}
                        onChange={handleClassChange}
                        placeholder="Select"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '250px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        {classDataWithAllClass?.map((item: ClassListInfo) => (
                          <MenuItem key={item.classId} value={item.className}>
                            {item.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth onClick={handleReset}>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box
            // bgcolor={changeView === true ? (isLight ? theme.palette.primary.lighter : theme.palette.grey[800]) : ''}
            >
              <Stack direction="row" justifyContent="space-between" alignItems="center" mx={5} mt={showFilter ? 0 : 2}>
                <ToggleButtonGroup
                  value={selectedFields}
                  onChange={handleFieldToggle}
                  aria-label="field selection"
                  size="small"
                  color="primary"
                  disabled={StudentListData.length === 0}
                >
                  {StudentsParentInfoColumns.map((col) => {
                    const isSelected = selectedFields.includes(col.name);
                    return (
                      <ToggleButton sx={{ fontWeight: 'bold' }} key={col.name} value={col.name}>
                        {isSelected && <SuccessIcon fontSize="small" sx={{mr:1}} />}
                        {col.headerLabel || col.name}
                      </ToggleButton>
                    );
                  })}
                </ToggleButtonGroup>
                <Button startIcon={<PictureAsPdfIcon />} variant="contained" color="info" onClick={handleExportPDF}>
                  Export to PDF
                </Button>
              </Stack>
              <Box className="student_table " sx={{ px: { xs: 3, md: 5 }, pb: { xs: 2, md: 2.5 } }}>
                <Paper className="card-table-container student_card" sx={{ marginTop: '12px' }}>
                  <DataTable
                    columns={filteredColumns}
                    data={StudentListData}
                    getRowKey={getRowKey}
                    fetchStatus="success"
                    // allowPagination
                    allowSorting
                    sortColumn={sortColumn}
                    sortDirection={sortDirection}
                    onSort={handleSort}
                    // PaginationProps={pageProps}
                    deletingRecords={deletingRecords}
                  />
                </Paper>
                {/* <Tooltip title="Export to Excel">
                    <IconButton color="primary" onClick={handleExportExcel}>
                      <GridOnIcon />
                    </IconButton>
                  </Tooltip> */}
              </Box>
            </Box>
          </div>
        </Card>
      </DownloadStudentInfoRoot>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="error" variant="filled" onClose={() => setSnackbarOpen(false)}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default DownloadStudentInfo;
