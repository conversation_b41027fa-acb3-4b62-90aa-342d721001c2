/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Stack, Button, Typography, Box, Card, IconButton, Chip } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { CloseIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import TextIcon from '@/Parent-Side/assets/TextIcon.svg';
import WordIcon from '@/Parent-Side/assets/WordIcon.svg';
import PowerpointIcon from '@/Parent-Side/assets/PowerpointIcon.svg';
import { MdAddCircle } from 'react-icons/md';

const ViewRoot = styled.div`
  width: 100%;
  padding: 1.5rem;
`;
const dummy = [
  { id: 2, fileIcon: TextIcon },
  { id: 3, fileIcon: WordIcon },
  { id: 4, fileIcon: PowerpointIcon },
];

function View() {
  const theme = useTheme();
  return (
    <ViewRoot>
      <Box>
        <Typography variant="subtitle2" mb={1} fontSize={16}>
          Question:
        </Typography>
        <Typography variant="subtitle1" fontSize={14}  mb={2}>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam quis, voluptatem minus placeat facere fugit
          quibusdam dolorem.
        </Typography>
        <Card sx={{ backgroundColor: theme.palette.grey[300], p: 2 }}>
          <Typography pb={2} variant="subtitle2" fontSize={16} >
            Submit Your Answer:
          </Typography>
          {dummy.map((item) => (
            <Card sx={{ p: 2, mb: 2 }} key={item.id}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" gap={3}>
                  <div>
                    <img src={item.fileIcon} alt="" />
                  </div>
                  <Typography variant="body2">File Name , Sept 05 : 2023 , 10 : 00 am</Typography>
                </Stack>
                <IconButton size="small">
                  <CloseIcon color="error" />
                </IconButton>
              </Box>
            </Card>
          ))}
          <Chip variant="outlined" size="small" color="primary" icon={<MdAddCircle />} label="Add File" />
        </Card>
        <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, pt: 3 }}>
          <Stack spacing={2} direction="row">
            <Button variant="contained" color="secondary">
              Cancel
            </Button>
            <Button variant="contained" color="success">
              Submit
            </Button>
          </Stack>
        </Box>
      </Box>
    </ViewRoot>
  );
}

export default View;
