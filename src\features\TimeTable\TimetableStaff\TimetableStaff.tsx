/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Box,
  Typography,
  Card,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Table,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { CLASS_SELECT, YEAR_SELECT } from '@/config/Selection';
import { TimetableIconMap } from '@/config/TimetableIconMap';
import { MdAdd } from 'react-icons/md';
import { TimetableSubjects } from '@/config/TimetableData';
import React from 'react';
import useSettings from '@/hooks/useSettings';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import Popup from '@/components/shared/Popup/Popup';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { Create } from './Create';

const TimetableStaffRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .MuiTableHead-root {
    position: sticky;
    left: 0px;
  }

  td {
    min-width: 110px;
  }
  .date {
    margin-left: auto;
  }

  .css-5m1m10-MuiTableCell-root:first-of-type {
    padding-left: 0;
  }
  .border_radius_none {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom: 0px;
  }
  .stdnt_name {
    color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[900] : props.theme.palette.common.white};
  }
  .Card {
    /* min-height: calc(100vh - 200px); */
    @media screen and (max-width: 1199.5px) {
      height: 100%;
    }
  }
`;

export const TimetableIcon = (props: any) => {
  const { Icon } = props;
  const icon = Icon ? TimetableIconMap[Icon] : null;
  return <img src={icon} alt={icon} />;
};
const SubjectCell = ({ subjects }: any) => {
  const theme = useTheme();
  return (
    <TableCell sx={{ border: `1px solid ${theme.palette.grey[300]} ` }} key={subjects.id}>
      <Paper
        sx={{
          minWidth: '100px',
          backgroundColor:
            (subjects.name === 'English' ? '#ecf9ff' : '') ||
            (subjects.name === 'Math' ? '#f3ebf9' : '') ||
            (subjects.name === 'Arabic' ? '#f2f7fb' : '') ||
            (subjects.name === 'Malayalam' ? '#fef4f7' : '') ||
            (subjects.name === 'Math' ? '#f2fbf9' : '') ||
            (subjects.name === 'Hindi' ? '#f2fbf9' : '') ||
            (subjects.name === 'Biology' ? '#f2faeb' : '') ||
            (subjects.name === 'Physics' ? '#ffe8e9' : '') ||
            (subjects.name === 'Social' ? '#fff1ea' : '') ||
            (subjects.name === 'Bussiness' ? '#ecf1fd' : '') ||
            (subjects.name === 'General' ? '#ebf2ff' : '') ||
            (subjects.name === 'Computer' ? '#eaf8fe' : '') ||
            (subjects.name === 'Economics' ? '#fff8eb' : '') ||
            (subjects.name === 'Accountancy' ? '#ecf8f2' : ''),
          px: 2,
          py: 1,
        }}
      >
        <Stack direction="column" display="flex" alignItems="center">
          <TimetableIcon Icon={subjects.icon} />
          <Typography variant="h6" fontSize={8} color="initial">
            VII-A
          </Typography>

          <Typography fontSize={8} fontWeight={600} color="initial">
            {subjects.name}
          </Typography>
        </Stack>
      </Paper>
    </TableCell>
  );
};
function TimetableStaff() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';

  const [createOpen, setCreateOpen] = React.useState(false);
  const [popup, setPopup] = React.useState(false);

  const handleClickOpen = () => {
    setCreateOpen(false);
    setPopup(true);
  };

  const handleClickClose = () => setPopup(false);

  const handleCreateClose = () => {
    setCreateOpen(false);
  };
  return (
    <Page title="TimetableStaff">
      <TimetableStaffRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between" mb={2} alignItems="center">
            <Typography variant="h6" fontSize={17} width="80%">
              Timetable Staff
            </Typography>
            <Box>
              <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={() => setCreateOpen(true)}>
                <MdAdd size="20px" /> Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <Grid pt={2} pb={4} container spacing={3}>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                renderInput={(params) => <TextField variant="outlined" {...params} placeholder="Select" />}
              />
            </Grid>
            <Grid item lg={4} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                renderInput={(params) => <TextField variant="outlined" {...params} placeholder="Select" />}
              />
            </Grid>

            <Grid item lg={4} xs={12}>
              <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                <Button variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>

          <Paper
            sx={{
              border: `1px solid #e8e8e9`,
              width: '100%',
              height: '100%',
              overflow: 'hidden',
            }}
          >
            <TableContainer
              sx={{
                width: { xs: '700px', md: '100%' },
                height: ' 433px',
              }}
            >
              <Table stickyHeader aria-label="simple table">
                <TableHead sx={{ zIndex: 1 }}>
                  <TableRow>
                    <TableCell
                      sx={{
                        position: 'sticky',
                        left: '0px',
                        zIndex: 10,
                        border: `1px solid ${theme.palette.grey[300]} `,
                      }}
                    >
                      {' '}
                    </TableCell>
                    {['01', '02', '03', '04', '05', '06', '07', '08'].map((day) => (
                      <TableCell sx={{ textAlign: 'center', border: `1px solid ${theme.palette.grey[300]} ` }}>
                        {day}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {TimetableSubjects.map((row) => (
                    <TableRow className="" key={row}>
                      <TableCell
                        sx={{
                          position: 'sticky',
                          left: '0px',
                          zIndex: 10,
                          border: `1px solid ${theme.palette.grey[300]} `,
                          backgroundColor: isLight ? theme.palette.common.white : theme.palette.grey[800],
                        }}
                        width="10%"
                      >
                        <Typography
                          variant="h6"
                          fontSize={12}
                          sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                        >
                          Staff Name
                        </Typography>
                      </TableCell>
                      {row.subjects.map((data) => (
                        <SubjectCell subjects={data} />
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Card>
      </TimetableStaffRoot>
      <TemporaryDrawer
        Title="Create"
        onClose={handleCreateClose}
        state={createOpen}
        DrawerContent={<Create onClose={handleCreateClose} open={handleClickOpen} />}
      />
      <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<SuccessMessage message="  Save Successfully" />}
      />
    </Page>
  );
}

export default TimetableStaff;
