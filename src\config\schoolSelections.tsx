import React from 'react';
import { Typography } from '@mui/material';
import passdaily<PERSON>ogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomas<PERSON>ogo from '@/assets/SchoolLogos/StThomasLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import alFitrahLogo from '@/assets/SchoolLogos/alFitrahLogo.png';

const schooolSelection = [
  {
    id: 0,
    logo: passdailyLogo,
    name: 'Passdaily',
    fullName: 'Passdaily',
    welcome: 'Welcome to Passdaily',
    image: 'http://demo.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Passdaily
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Palakad, Kerala.
        </Typography>
      </>
    ),
  },
  {
    id: 1,
    logo: holyLogo,
    name: 'Holy Angels',
    fullName: 'Holy Angels School',
    welcome: 'Welcome to Holy Angels School',
    image: 'http://holy.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Holy Angels
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Dombivli, Mumbai, Maharashtra.
        </Typography>
      </>
    ),
  },
  {
    id: 1,
    logo: holyLogo,
    name: 'Holy Angels Gardens',
    fullName: 'Holy Angels Gardens School',
    welcome: 'Welcome to Holy Angels Gardens School',
    image: 'http://holy.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
          <b>ANGELS&apos; GARDENS</b>
        </Typography>
        <Typography variant="body1" fontSize={13}>
          The Kindergarten Experience
        </Typography>
        <Typography variant="body1" fontSize={15}>
          Gandhinagar, Dombivli (E)
        </Typography>
      </>
    ),
  },
  {
    id: 1,
    logo: holyLogo,
    name: 'Holy Angels',
    fullName: 'Holy Angels School',
    welcome: 'Welcome to Holy Angels School',
    image: 'http://holy.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Holy Angels Paradise
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Dombivli, Mumbai, Maharashtra.
        </Typography>
      </>
    ),
  },
  {
    id: 1,
    logo: holyLogo,
    name: 'Holy Angels',
    fullName: 'Holy Angels School',
    welcome: 'Welcome to Holy Angels School',
    image: 'http://holy.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Holy Angels Paradise
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Dombivli, Mumbai, Maharashtra.
        </Typography>
      </>
    ),
  },
  {
    id: 2,
    logo: carmelLogo,
    name: 'Carmel',
    fullName: 'Carmel Convent School',
    welcome: 'Welcome to Carmel Convent School',
    image: 'http://carmel.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Carmel Convent School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Bhanvaj road, Khopoli - 410 203, Dist. <br /> Raigad, Maharashtra.
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, +919763642887
        </Typography>
      </>
    ),
  },
  {
    id: 3,
    logo: thereseLogo,
    name: 'St Therese',
    fullName: 'St Therese Convent High School',
    welcome: 'Welcome to St.Therese Convent School',
    image: 'http://therese.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          St Therese Convent High School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Nirmalwadi, Kharadi -411014,
          <br /> Tal- Haveli, Dist- Pune
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 9765645776
        </Typography>
      </>
    ),
  },
  {
    id: 4,
    logo: thomasLogo,
    name: 'St Thomas',
    fullName: 'St Thomas School',
    welcome: 'Welcome St.Thomas',
    image: 'http://stm.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          St Thomas School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Behind Ebenzer Church, Near Shankeshwar Nagar
          <br />
          Nandivil-Bhoper Road, Dombivli(E) - 421 201
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 8928851277
        </Typography>
      </>
    ),
  },
  {
    id: 5,
    logo: nirmalaLogo,
    name: 'Nirmala',
    fullName: 'Nirmala Convent School',
    welcome: 'Welcome to Nirmal Convent School Pune',
    image: 'http://nirmala.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Nirmala Convent School
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          Nirmalwadi, Kharadi -411014,
          <br /> Tal- Haveli, Dist- Pune
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize={13} color="secondary">
          <EMAIL>, 9765645776
        </Typography>
      </>
    ),
  },
  {
    id: 6,
    logo: alFitrahLogo,
    name: 'Al Fitrah',
    fullName: 'Al Fitrah School',
    welcome: 'Welcome to Al Fitrah School',
    image: 'http://alfitrah.passdaily.in/EventFile/',
    address: () => <></>,
  },
  {
    id: 7,
    logo: MIMLogo,
    name: 'MIM School',
    fullName: 'Mueenul Islam Manoor High School',
    welcome: 'Welcome to MIM High School',
    image: 'http://mim.passdaily.in/EventFile/',
    address: () => (
      <>
        <Typography variant="subtitle2" fontSize={16} color="primary">
          Mueenul Islam Manoor High School
        </Typography>
        <Typography variant="body1" fontSize={13} color="secondary">
          KANDANAKAM,KALADI PO MALAPPURAM 679582
        </Typography>
        <Typography variant="body1" fontSize={13} color="secondary">
          04942103095,9645942121
        </Typography>
      </>
    ),
  },
];

export default schooolSelection;
