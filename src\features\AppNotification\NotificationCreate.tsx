/* eslint-disable no-else-return */
import React, { useCallback, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  TextField,
  Box,
  Typography,
  Stack,
  Button,
  Card,
  Divider,
  Grid,
  FormControl,
  IconButton,
  InputAdornment,
  CardMedia,
  useTheme,
  Tooltip,
  Select,
  MenuItem,
} from '@mui/material';
import styled from 'styled-components';
import TextareaField from '@/components/shared/Selections/TextareaField';
import { ErrorIcon, SuccessIcon } from '@/theme/overrides/CustomIcons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import FilesUpload from '@/components/shared/Selections/FilesUpload';
import { fileUpload } from '@/store/MessageBox/messageBox.thunks';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { v4 as uuidv4 } from 'uuid';
import { getNotificationSubmitting } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import uploadFiles from '@/assets/NotificationIcons/upload.gif';
import ErrorNotification1 from '@/assets/MessageIcons/error-message.gif';
import ErrorNotification2 from '@/assets/MessageIcons/error-msg-template.gif';
import SaveFile from '@/assets/MessageIcons/save-file.gif';
import CloseIcon from '@mui/icons-material/Close';
import useSettings from '@/hooks/useSettings';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import Files from '@/assets/NotificationIcons/docsFile.png';
import doc from '@/assets/NotificationIcons/doc.png';
import pdf from '@/assets/NotificationIcons/pdf.png';
import xls from '@/assets/NotificationIcons/xls.png';
import ppt from '@/assets/NotificationIcons/ppt.png';
import { NotificationDataType } from '@/types/Notification';
import { createNotification } from '@/store/AppNotification/appNotification.thunks';
import useAuth from '@/hooks/useAuth';
import { STATUS_OPTIONS } from '@/config/Selection';
import LoadingButton from '@mui/lab/LoadingButton';
import ImageUploadWithCropper from '@/components/shared/ImageUploadWithCropper';
import ImageCrop from '@/components/shared/ImageCrop';
import SaveIcon from '@mui/icons-material/Save';

const NotificationCreateRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
`;

type FileObjTypes = {
  id: number;
  name: string;
  type: string;
  imageUrl?: string;
  originalFile: File;
};

function NotificationCreate() {
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { confirm } = useConfirm();
  const [uploaded, setUploaded] = useState<FileObjTypes[]>([]);
  const isSubmitting = useAppSelector(getNotificationSubmitting);
  const [uploadedFile, setUploadedFile] = useState<FileObjTypes | undefined>();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const randomId = Math.floor(Math.random() * 1_000_000);
      const imgUrl = URL.createObjectURL(file);
      const fileObj = {
        id: randomId,
        name: file.name,
        type: file.type,
        imageUrl: imgUrl,
        originalFile: file,
      };
      setUploadedFile(fileObj);
      if (uploadFiles) {
        setIsDialogOpen(true);
      }
      console.log('Uploaded File1:', file);
    } else {
      const files = Array.from(event.currentTarget.files || []);

      const fileObjects = files.map((f) => {
        const randomId = Math.floor(Math.random() * 1_000_000);
        return {
          id: randomId,
          name: f.name,
          type: f.type,
          originalFile: f,
        };
      });
      console.log('fileObjects', fileObjects);
      console.log('files', files);

      // Update the state with the newly created file objects
      setUploaded((prevUploaded) => [...prevUploaded, ...fileObjects]);
    }
  };
  // const isProgress = useAppSelector(getProgressUploading);

  // const handleSave = async () => {
  //   try {
  //     const formData = new FormData();
  //     uploaded.forEach((file) => {
  //       formData.append('files', file);
  //     });

  //     const response = await dispatch(
  //       fileUpload({
  //         files: formData,
  //         onUploadProgress: (progressEvent) => {
  //           console.log(Math.round((progressEvent.loaded / progressEvent.total) * 100));
  //           const percentCompleted = Math.round((progressEvent.loaded / progressEvent.total) * 100);
  //           setProgress(percentCompleted); // Update progress
  //           dispatch(setProgress(percentCompleted)); // Dispatch progress
  //         },
  //       })
  //     ).unwrap();
  //     setUploadedFile(response.details);
  //     const errorMessages = response.result === 'Failed';
  //     const successMessages = response.result === 'Success';

  //     if (!errorMessages) {
  //       const successMessage = (
  //         <SuccessMessage icon={uploadSuccess} message={`${response.details} File uploading successfully`} />
  //       );
  //       await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //       setUploaded([]);
  //     } else if (!successMessages) {
  //       const errorMessage = <ErrorMessage icon={uploadError} message="File uploading failed " />;
  //       await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //     } else {
  //       const errorMessage = <ErrorMessage icon={uploadError} message="Error file uploading" />;
  //       await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //     }
  //   } catch (error) {
  //     console.error(error);
  //     const errorMessage = (
  //       <ErrorMessage icon={uploadError} message="Error file uploading, Please recheck and Try again" />
  //     );
  //     await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //   }
  // };

  // const handleSave = useCallback(
  //   async (value: NotificationDataType) => {
  //     console.log('value', value);
  //     // // setMessageSave(value);
  //     // console.log('messageSave', messageSave);
  //     try {
  //       if (uploaded.length !== 0) {
  //         const formData = new FormData();
  //         uploaded.forEach((file) => {
  //           formData.append('files', file);
  //         });

  //         const response = await dispatch(
  //           fileUpload({
  //             files: formData,
  //             onUploadProgress: (progressEvent) => {
  //               console.log(Math.round((progressEvent.loaded / progressEvent.total) * 100));
  //               const percentCompleted = Math.round((progressEvent.loaded / progressEvent.total) * 100);
  //               setProgress(percentCompleted); // Update progress
  //               dispatch(setProgress(percentCompleted)); // Dispatch progress
  //             },
  //           })
  //         ).unwrap();
  //         setUploadedFile(response.details);
  //         console.log('response.details::::', response.details);
  //       }

  //       const { ...rest } = value;
  //       console.log('value::::', value);
  //       const response = await dispatch(createNotification(rest)).unwrap();
  //       console.log('response::::', response);
  //       setMsgId(response.id);

  //       if (response.id > 0) {
  //         const successMessage = <SuccessMessage message="Message created successfully" />;
  //         await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
  //         // Find the newly created message in messageTempListDatas
  //       }

  //       // const successMessage = <SuccessMessage icon={AddFile} message="Message created successfully" />;
  //       // await confirm(successMessage, 'Message Created', { okLabel: 'Ok', showOnlyOk: true });
  //     } catch (error) {
  //       const errorMessage = <ErrorMessage message="Message content already created" />;
  //       await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
  //       console.error(error);
  //     }
  //   },
  //   [confirm, dispatch]
  // );

  // const handleSave = useCallback(
  //   async (value: NotificationDataType) => {
  //     console.log('value', value);
  //   },
  //   [confirm, dispatch, uploaded]
  // );

  const renderFileIcon = (file: { id: string; imageUrl: string; type: string; name: string; originalFile: File }) => {
    const { imageUrl, name, type } = file;
    const tooltipText = `${name}`;
    console.log('name', name);
    console.log('file', file);

    if ((imageUrl && type?.startsWith('image/png')) || type?.startsWith('image/jpeg')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={imageUrl} alt="Uploaded" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
          {/* <CardMedia sx={{ objectFit: 'contain', height: '100%' }} component="img" image={file} alt="" /> */}
        </Tooltip>
      );
    } else if (type?.startsWith('video/')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <CardMedia height="100%" component="video" controls src={imageUrl} />
        </Tooltip>
      );
    } else if (name?.toLowerCase().endsWith('.pdf')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={pdf} alt="PDF" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (name?.toLowerCase().endsWith('.doc') || name?.toLowerCase().endsWith('.docx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={doc} alt="DOCX" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (name?.toLowerCase().endsWith('.xls') || name?.toLowerCase().endsWith('.xlsx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={xls} alt="XLSX" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else if (name?.toLowerCase().endsWith('.ppt') || name?.toLowerCase().endsWith('.pptx')) {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={ppt} alt="PPT" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip title={tooltipText} arrow>
          <img src={Files} alt="FILES" style={{ height: '100%', cursor: 'pointer' }} />
        </Tooltip>
      );
    }
  };

  const handleRemoveFile = (index: number) => {
    setUploaded((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const handleOpenDialog = (file: FileObjTypes) => {
    console.log('Uploaded File2', file);
    setUploadedFile(file);
    setIsDialogOpen(true);
  };

  const CreateEditNotifiicationValidationSchema = Yup.object({
    notificationTitle: Yup.string().required('Please enter subject'),
    notificationContent: Yup.string().required('Please enter content'),
    notificationStatus: Yup.number().oneOf([0, 1], 'Please select status'),
  });

  const {
    values: { notificationTitle, notificationContent, notificationStatus },
    handleChange,
    handleBlur,
    handleSubmit,
    handleReset,
    setFieldValue,
    touched,
    errors,
    resetForm,
  } = useFormik<NotificationDataType>({
    initialValues: {
      notificationId: 0,
      notificationTitle: '',
      notificationContent: '',
      notificationFile: '',
      adminId,
      fileCount: 0,
      createdDate: '',
      notificationStatus: 1,
      accademicId: 11,
    },
    validationSchema: CreateEditNotifiicationValidationSchema,
    onSubmit: async (values) => {
      // handleSave(values);
      console.log('values', values);
      try {
        let uploadedFilesDetails = null;

        // Upload files if any
        if (uploaded.length !== 0) {
          console.log('uploaded', uploaded);

          const formData = new FormData();
          uploaded.forEach((file) => {
            formData.append('files', file.originalFile);
          });

          const uploadResponse = await dispatch(
            fileUpload({
              files: formData,
            })
          ).unwrap();

          uploadedFilesDetails = uploadResponse.details;
          console.log('uploadedFilesDetails', uploadedFilesDetails);
        }

        // Add uploaded file details to the value
        const rest = { ...values, notificationFile: uploadedFilesDetails };
        console.log('rest::::', rest);

        // Create notification
        const response = await dispatch(createNotification(rest)).unwrap();
        console.log('response', response);

        const Success = response.result === 'SUCCESS';
        const Exist = response.result === 'EXIST';

        if (response.id > 0 || Success) {
          const successMessage = <SuccessMessage icon={SaveFile} message="Notification created successfully" />;
          await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          setUploaded([]);
          resetForm();
        } else if (Exist) {
          const errorMessage = <ErrorMessage icon={ErrorNotification1} message="Notification already created" />;
          await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      } catch (error) {
        const errorMessage = (
          <ErrorMessage icon={ErrorNotification2} message="Something went wrong, please try again." />
        );
        await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        console.error(error);
      }
    },
    validateOnBlur: false,
  });

  return (
    <Page title="Notification Create">
      <NotificationCreateRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Notification Create
          </Typography>
          <Divider />
          <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
            <Stack py={2}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Subject
                    </Typography>
                    <TextField
                      placeholder="Enter subject"
                      name="notificationTitle"
                      value={notificationTitle}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      error={touched.notificationTitle && !!errors.notificationTitle}
                      helperText={touched.notificationTitle && errors.notificationTitle}
                      // disabled={isSubmitting}
                      InputProps={{
                        endAdornment: touched.notificationTitle && !!errors.notificationTitle && (
                          <ErrorIcon color="error" />
                        ),
                      }}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Status
                    </Typography>
                    <Select name="notificationStatus" value={Number(notificationStatus)} onChange={handleChange}>
                      {STATUS_OPTIONS.map((opt) => (
                        <MenuItem key={opt.id} value={opt.id}>
                          {opt.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {touched.notificationStatus && !!errors.notificationStatus && (
                      <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                        {errors.notificationStatus}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Typography variant="subtitle1" fontSize={12} color="GrayText">
                      Content
                    </Typography>
                    <TextareaField
                      limit={1000}
                      placeholder="Enter content..."
                      name="notificationContent"
                      value={notificationContent}
                      onChange={handleChange}
                      error={touched.notificationContent && !!errors.notificationContent}
                      helperText={touched.notificationContent && errors.notificationContent}
                      InputProps={{
                        inputProps: {
                          style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                          maxLength: 1000,
                        },
                        endAdornment: touched.notificationContent && !!errors.notificationContent && (
                          <InputAdornment position="end">
                            <ErrorIcon color="error" sx={{ mr: 2 }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Stack>
            <Box gap={5}>
              {/* <Stack width={{ xs: '100%', lg: '50%' }}>
                <FormControl fullWidth>
                  <Typography variant="subtitle1" fontSize={12}>
                    Upload Files
                  </Typography>
                  <FilesUpload
                    height={150}
                    name="notificationFile"
                    accept="/*"
                    multiple
                    onChange={(event) => {
                      const files = Array.from(event.currentTarget.files || []);
                      // setFieldValue('notificationFile', files);
                      setUploaded((prevUploaded) => [...prevUploaded, ...files]);
                    }}
                  />
                </FormControl>
              </Stack> */}
              {/* <ImageCrop /> */}
              <Box mt={1} display="flex" flexWrap="wrap" gap={1}>
                <ImageUploadWithCropper
                  uploadedFile={uploadedFile}
                  setUploaded={setUploaded}
                  uploaded={uploaded}
                  setIsDialogOpen={setIsDialogOpen}
                  isDialogOpen={isDialogOpen}
                  onImageUpload={handleImageUpload}
                  uploadButton={
                    <Stack width={{ xs: '100%', lg: '50%' }}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12} color="GrayText">
                          Upload Files
                        </Typography>
                        <FilesUpload
                          height={150}
                          name="notificationFile"
                          accept="/*"
                          multiple
                          onChange={(event) => {
                            handleImageUpload(event); // Ensure the function is called properly
                            // const files = Array.from(event.currentTarget.files || []); // Safely convert FileList to an array
                            // setUploaded((prevUploaded) => [...prevUploaded, ...files]); // Append new files to the existing state
                          }}
                        />
                      </FormControl>
                    </Stack>
                  }
                />
                {uploaded.map((file: any, index: number) => (
                  <Card
                    key={uuidv4()}
                    sx={{
                      boxShadow: 4,
                      position: 'relative',
                      height: 200,
                      width: file?.type?.startsWith('video/') || file?.type?.startsWith('image/') ? 250 : 'fit-content',
                      p: !(file?.type?.startsWith('video/') || file?.type?.startsWith('image/')) ? 2 : 0,
                    }}
                  >
                    <IconButton
                      size="small"
                      sx={{
                        position: 'absolute',
                        right: 5,
                        top: 5,
                        zIndex: 1,
                        p: 0.5,
                        border: `1px solid ${theme.palette.grey[200]}`,
                        backgroundColor: theme.palette.common.white,
                        '&:hover': {
                          backgroundColor: theme.palette.error.main,
                          color: theme.palette.common.white,
                        },
                      }}
                      onClick={() => handleRemoveFile(index)}
                    >
                      <CloseIcon sx={{ fontSize: '18px' }} />
                    </IconButton>
                    {file?.type?.startsWith('image/') && (
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          right: 5,
                          top: 35,
                          zIndex: 1,
                          p: 0.5,
                          border: `1px solid ${theme.palette.grey[200]}`,
                          backgroundColor: theme.palette.common.white,
                          '&:hover': {
                            backgroundColor: theme.palette.grey[500],
                            color: theme.palette.grey[200],
                          },
                        }}
                        onClick={() => handleOpenDialog(file)}
                      >
                        <ModeEditIcon sx={{ fontSize: '18px' }} />
                      </IconButton>
                    )}
                    {renderFileIcon(file)}
                  </Card>
                ))}
              </Box>
            </Box>

            <Box display="flex" sx={{ justifyContent: { xs: 'end', md: 'end' }, pr: { lg: '5' }, pt: 3 }}>
              {/* <Box position="absolute" bottom="1.5rem" right='1.5rem'> */}
              <Stack spacing={2} direction="row">
                <Button disabled={isSubmitting} variant="contained" color="secondary">
                  Cancel
                </Button>
                <LoadingButton
                  loadingPosition="start"
                  fullWidth
                  loading={isSubmitting}
                  startIcon={<SaveIcon />}
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </LoadingButton>
              </Stack>
            </Box>
          </form>
        </Card>
      </NotificationCreateRoot>
      {isSubmitting && (
        <LoadingPopup
          // title="Message Creating"
          popupContent={<LoadingMessage icon={uploadFiles} message="Files uploading please wait..." />}
        />
      )}
      {/* <Popup
        size="xs"
        state={popup}
        onClose={handleClickClose}
        popupContent={<PublishMessage message="Do you want publish the audio?" />}
      /> */}
    </Page>
  );
}

export default NotificationCreate;
