/* eslint-disable jsx-a11y/alt-text */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  IconButton,
} from '@mui/material';
import styled from 'styled-components';
import NotSuccess from '@/assets/attendance/notApprove.svg';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import Popup from '@/components/shared/Popup/Popup';
import { CLASS_SELECT, YEAR_SELECT, DAY_SELECT } from '@/config/Selection';
import { TIME_TABLE_DATA, TimeTableDataProps } from '@/config/TableData';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

const ListTimeTableRoot = styled.div`
  padding: 1rem;
  .Card {
    min-height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }
  }
  .student_name {
    width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
  .student_name2 {
    width: 130px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
  }
`;

function ListTimeTable() {
  const [Delete, setDelete] = React.useState(false);
  const [TimeTableData, setTimeTableData] = useState<TimeTableDataProps[] | undefined>([]);
  const [year, setYear] = useState<string | null>('');
  const [Class, setClass] = useState<string | null>('');
  const [Day, setDay] = useState<string | null>('');

  const handleClickDelete = () => setDelete(true);
  const handleClickCloseDelete = () => setDelete(false);
  const handleSearch = () => {
    const findData: TimeTableDataProps[] | undefined =
      TIME_TABLE_DATA && TIME_TABLE_DATA?.length > 0
        ? TIME_TABLE_DATA?.filter((list) => list?.year === year && list?.class === Class && list?.day === Day)
        : undefined;
    setTimeTableData(findData);
    console.log('filterdata', findData);
  };

  return (
    <Page title="TimeTable List">
      <ListTimeTableRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Timetable List
          </Typography>
          <Divider />
          <Grid pb={4} container spacing={3} pt={2}>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Year
              </Typography>
              <Autocomplete
                options={YEAR_SELECT}
                value={year}
                onChange={(e, v) => setYear(v)}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setYear(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Class
              </Typography>
              <Autocomplete
                options={CLASS_SELECT}
                onChange={(e, v) => setClass(v)}
                value={Class}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setClass(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Typography variant="h6" fontSize={14}>
                Select Day
              </Typography>
              <Autocomplete
                options={DAY_SELECT}
                onChange={(e, v) => setDay(v)}
                value={Day}
                renderInput={(params) => (
                  <TextField onChange={({ target }) => setDay(target.value)} {...params} placeholder="Select" />
                )}
              />
            </Grid>
            <Grid item lg={3} xs={12}>
              <Stack spacing={2} sx={{ pt: { xs: 0, md: 3.79 } }} direction="row">
                <Button onClick={() => {}} variant="contained" color="secondary" fullWidth>
                  Reset
                </Button>
                <Button onClick={handleSearch} variant="contained" color="primary" fullWidth>
                  Search
                </Button>
              </Stack>
            </Grid>
          </Grid>
          {TimeTableData && TimeTableData?.length === 0 && (
            <Stack
              direction="row"
              display="flex"
              justifyContent="center"
              alignItems="center"
              height="calc(100vh - 390px)"
              width="100%"
            >
              <Box>
                <img src={NotSuccess} alt="" width={150} />
                <Typography textAlign="center" pt={2} variant="body2">
                  No data found.
                </Typography>
              </Box>
            </Stack>
          )}
          {TimeTableData && TimeTableData?.length > 0 && (
            <Paper
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                height: '100%',
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  height: 0,
                },
              }}
            >
              <TableContainer
                sx={{
                  width: { xs: '650px', md: '100%' },
                }}
              >
                <Table stickyHeader aria-labelledby="tableTitle">
                  <TableHead>
                    <TableCell>Sl No.</TableCell>
                    <TableCell>Academic Year</TableCell>
                    <TableCell>Class</TableCell>
                    <TableCell>Subject</TableCell>
                    <TableCell>Day</TableCell>
                    <TableCell>Period</TableCell>
                    <TableCell>Action</TableCell>
                  </TableHead>
                  <TableBody>
                    {TimeTableData &&
                      TimeTableData?.length > 0 &&
                      TimeTableData?.map((row, index) => {
                        return (
                          <TableRow>
                            <TableCell sx={{ fontWeight: 600 }} width="10%">
                              {index + 1}
                            </TableCell>
                            <TableCell sx={{ fontWeight: 600 }} width="15%">
                              {row.year}
                            </TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>{row.class}</TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>{row.name}</TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>{row.day}</TableCell>
                            <TableCell sx={{ fontWeight: 600 }}>{index + 1}</TableCell>
                            <TableCell width="10%">
                              <Stack direction="row" gap={1}>
                                <IconButton size="small">
                                  <ModeEditIcon />
                                </IconButton>
                                <IconButton onClick={handleClickDelete} size="small" color="error">
                                  <DeleteIcon />
                                </IconButton>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          )}
        </Card>
        <Popup
          size="xs"
          state={Delete}
          onClose={handleClickCloseDelete}
          popupContent={<DeleteMessage message="Do you want delete from the list?" />}
        />
      </ListTimeTableRoot>
    </Page>
  );
}

export default ListTimeTable;
