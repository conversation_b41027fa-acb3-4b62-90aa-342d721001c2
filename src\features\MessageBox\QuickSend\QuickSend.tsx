/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-lonely-if */
/* eslint-disable no-plusplus */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Page from '@/components/shared/Page';
import {
  Grid,
  TextField,
  Box,
  Typography,
  Stack,
  Button,
  Card,
  Divider,
  InputAdornment,
  FormControl,
  MenuItem,
  Select,
  TableContainer,
  TableBody,
  TableCell,
  Table,
} from '@mui/material';
import {
  createMessageTempList,
  messageSendToAllConveyors,
  messageSendToAllParents,
  messageSendToAllPta,
  messageSendToAllStaff,
  fetchMessageTemplate,
} from '@/store/MessageBox/messageBox.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import styled from 'styled-components';
import MuiSwitch from '@/components/shared/Selections/MuiSwitch';
import { MESSAGE_TYPE_OPTIONS } from '@/config/Selection';
import { useFormik } from 'formik';
import { MessageTempDataType } from '@/types/MessageBox';
import useAuth from '@/hooks/useAuth';
import * as Yup from 'yup';
import { SendButtonType } from '@/features/MessageBox/SmsTemplate/SmsTemplete';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import LoadingMsg from '@/assets/MessageIcons/loading-message.gif';
import ErrorMsg from '@/assets/MessageIcons/error-message.gif';
import ErrorMsg1 from '@/assets/MessageIcons/error-message1.gif';
import SuccessMsg from '@/assets/MessageIcons/message-success.gif';
import Popup from '@/components/shared/Popup/Popup';
import SendMessage from '@/features/MessageBox/SmsTemplate/SendMessage';
import TextareaField from '@/components/shared/Selections/TextareaField';
// import TempDrawer from './TempDrawer';

const QuickSendRoot = styled.div`
  padding: 1rem;

  .sub-heading {
    font-size: 90%;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .Card {
    height: calc(100vh - 160px);
  }
  .message {
    font-size: 13px;
    padding-left: 1rem;
    color: ${(props) => props.theme.palette.grey[600]};
  }
  .MuiTableCell-root {
    border-bottom: 0px;
  }
`;

const CreateMessageTempValidationSchema = Yup.object({
  messageTitle: Yup.string().required('Please enter Message Title'),
  messageType: Yup.number().oneOf([1, 2, 3], 'Please select Message Type'),
  messageContent: Yup.string().required('Please enter Message Content'),
});

export const quicksendlinks = [
  { id: 1, name: 'PTA', component: 'PTA' },
  { id: 2, name: 'Class Wise', component: 'ClassWise' },
  { id: 3, name: 'Class Division Wise', component: 'ClassDivision' },
  { id: 4, name: 'Group Wise', component: 'GroupWise' },
  { id: 5, name: 'Conveyors', component: 'Conveyors' },
  { id: 6, name: 'Public Group Wise', component: 'PublicGroups' },
  { id: 7, name: 'Public Groups Individual', component: 'PublicGroups' },
];

export const quicksendlinks2 = [
  { id: 1, name: 'Staff Individual', component: 'Staffs' },
  { id: 2, name: 'Student Individual', component: 'Parents' },
  { id: 3, name: 'Groups Individual', component: 'Groups' },
];

export const sendbuttonsAll = [
  { id: '1', name: 'All Parents', action: messageSendToAllParents },
  { id: '2', name: 'All Staffs', action: messageSendToAllStaff },
  { id: '3', name: 'All PTA', action: messageSendToAllPta },
  { id: '4', name: 'All Conveyors', action: messageSendToAllConveyors },
];

function QuickSend() {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const accountId: number | undefined = user?.accountId;
  const [open, setOpen] = React.useState(false);
  const [messageTemp, setMessageTemp] = React.useState<MessageTempDataType>({});
  const [active, setActive] = React.useState<SendButtonType | null>(null);
  const [allSwitch, setAllSwitch] = React.useState<any>(null);

  const handleSendAll = React.useCallback(
    async (item: any, msgId: number) => {
      const sendConfirmMessage = (
        <LoadingMessage
          icon={LoadingMsg}
          message={
            <div>
              Are you sure you want to send to <br />
              &quot;{item.name}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Send Message?', { okLabel: 'Send', cancelLabel: 'Cancel' })) {
        const sendResponse = await dispatch(
          item?.action({ adminId: accountId, academicId: 10, messageId: msgId })
        ).unwrap();
        console.log(sendResponse);
        if (sendResponse.result === 'Success') {
          const sendDoneMessage = <LoadingMessage icon={SuccessMsg} message="Message Sent successfully." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else if (sendResponse.result === 'Failed') {
          const sendDoneMessage = <LoadingMessage icon={ErrorMsg} message="Message Sending Failed." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const sendDoneMessage = <LoadingMessage icon={ErrorMsg1} message="Something Went Wrong Please try later." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      }
    },
    [dispatch, confirm, accountId]
  );
  const {
    values: {
      messageTitle,
      messageContent,
      messageType,
      messageTemplateId,
      messageId,
      messageDate,
      messageStatus,
      messageCreatedBy,
    },
    handleChange,
    handleBlur,
    handleSubmit,
    handleReset,
    touched,
    errors,
  } = useFormik<MessageTempDataType>({
    initialValues: {
      messageId: 0,
      messageDate: '',
      messageTitle: '',
      messageContent: '',
      messageType: 3,
      messageStatus: 1,
      messageTemplateId: '',
      messageCreatedBy: accountId,
    },
    validationSchema: CreateMessageTempValidationSchema,
    onSubmit: async (value) => {
      if (
        value.messageTitle !== messageTemp.messageTitle ||
        value.messageContent !== messageTemp.messageContent ||
        value.messageType !== messageTemp.messageType
      ) {
        console.log(messageTemp);
        const response = await dispatch(createMessageTempList(value)).unwrap();
        if (response.id) {
          const response2 = await dispatch(fetchMessageTemplate(response.id)).unwrap();
          if (response2) {
            setMessageTemp(response2);
            console.log('Message', response2);
          }
          if (allSwitch === null && active === null) {
            const sendDoneMessage = <LoadingMessage icon={SuccessMsg} message="Message Saved to Template list" />;
            await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            if (allSwitch === null) {
              console.log('active', active);
              setOpen(true);
            } else {
              console.log('allSwitch', allSwitch);
              console.log('messageTemp', messageTemp);
              handleSendAll(allSwitch, response.id);
            }
          }
        } else {
          const sendDoneMessage = <LoadingMessage icon={ErrorMsg1} message="Something Went Wrong Please try later." />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        }
      } else {
        if (allSwitch === null && active === null) {
          const sendDoneMessage = <LoadingMessage icon={SuccessMsg} message="Message Saved to Template list" />;
          await confirm(sendDoneMessage, '', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          if (allSwitch === null) {
            console.log('active', active);
            setOpen(true);
          } else {
            console.log('allSwitch', allSwitch);
            console.log('messageTemp', messageTemp);
            handleSendAll(allSwitch, messageTemp?.messageId);
          }
        }
      }
    },
    validateOnBlur: false,
  });
  const showTemplateIdField = messageType === 2;
  const limit = messageType === 1 ? 30 : 1000;

  const handleClickClose = () => {
    setOpen(false);
    setActive(null);
    setAllSwitch(null);
    // handleReset();
  };

  const handleSwitch = (event: number, switchValue: SendButtonType) => {
    setActive((prevSwitch) => (prevSwitch === switchValue ? null : switchValue));
    setAllSwitch(null);
  };

  const handleSwitchChange = (event: number, switchValue: number) => {
    setActive(null);
    setAllSwitch((prevSwitch: any) => (prevSwitch === switchValue ? null : switchValue));
  };

  return (
    <Page title="Quick Send">
      <QuickSendRoot>
        <Card className="Card" elevation={5} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Typography variant="h6" fontSize={17}>
            Quick Send
          </Typography>
          <form noValidate onSubmit={handleSubmit} onReset={handleReset}>
            <Grid container spacing={{ xl: 10, xs: 3 }}>
              <Grid item xs={12} lg={6}>
                <Stack py={2}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12}>
                          Message Title
                        </Typography>
                        <TextField
                          placeholder="Enter message title"
                          name="messageTitle"
                          value={messageTitle}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={touched.messageTitle && !!errors.messageTitle}
                          helperText={touched.messageTitle && errors.messageTitle}
                          // disabled={isSubmitting}
                          InputProps={{
                            endAdornment: (
                              <>{touched.messageTitle && !!errors.messageTitle && <ErrorIcon color="error" />}</>
                            ),
                          }}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12}>
                          Type
                        </Typography>
                        <Select
                          name="messageType"
                          value={messageType}
                          onChange={handleChange}
                          error={touched.messageType && !!errors.messageType}
                          // disabled={isSubmitting}
                        >
                          {MESSAGE_TYPE_OPTIONS.map((opt) => (
                            <MenuItem key={opt.id} value={opt.id}>
                              {opt.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {touched.messageType && !!errors.messageType && (
                          <Typography color="red" fontSize="12px" ml={2.5} mt={1} variant="subtitle1">
                            {errors.messageType}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <Typography variant="subtitle1" fontSize={12}>
                          Message content
                        </Typography>
                        <TextareaField
                          limit={limit}
                          placeholder="Enter content..."
                          name="messageContent"
                          value={messageContent}
                          onChange={handleChange}
                          error={touched.messageContent && !!errors.messageContent}
                          helperText={touched.messageContent && errors.messageContent}
                          InputProps={{
                            inputProps: {
                              style: { resize: 'vertical', minHeight: '60px', maxHeight: '100px' },
                              maxLength: limit,
                            },
                            endAdornment: (
                              <>
                                {touched.messageContent && !!errors.messageContent && (
                                  <InputAdornment position="end">
                                    <ErrorIcon color="error" sx={{ mr: 2 }} />
                                  </InputAdornment>
                                )}
                              </>
                            ),
                          }}
                        />
                      </FormControl>
                    </Grid>
                    {showTemplateIdField && (
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <Typography variant="subtitle1" fontSize={12}>
                            Template Id
                          </Typography>
                          <TextField
                            placeholder="Enter template id"
                            name="messageTemplateId"
                            value={messageTemplateId}
                            onChange={handleChange}
                            error={touched.messageTemplateId && !!errors.messageTemplateId}
                            helperText={errors.messageTemplateId}
                          />
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>
                </Stack>

                <Box pt={3} sx={{ display: 'flex', gap: { xl: 0, xxl: 0 }, flexWrap: 'wrap' }}>
                  <ul className="message" style={{ marginRight: 20 }}>
                    <li>Please do not copy paste message from Word, Excel, Notepad etc.</li>
                    <li>
                      If copied, Please ensure the special character like @ ,# ,$ ,& ,&quot; ,etc are not included.
                    </li>
                  </ul>
                  <ul className="message">
                    <li>Or please remove special characters then retype from the keyboard.</li>
                    <li>Kindly avoid unwanted space and next line for better delivery.</li>
                  </ul>
                </Box>
              </Grid>

              <Grid item xs={12} lg={6}>
                <Box>
                  <Typography variant="h6" fontSize={15}>
                    Send To
                  </Typography>
                </Box>
                <Box
                // width={{ xl: '50%', lg: '70%', md: '100%', sm: '50%', xs: '100%' }}
                >
                  <Grid pt={2} container spacing={2}>
                    <Grid item md={6} xs={12}>
                      {sendbuttonsAll?.map((row: any) => (
                        <TableContainer>
                          <Table key={row.id}>
                            <TableBody>
                              <TableCell>
                                <Typography sx={{ width: 'fit-content', cursor: 'pointer' }} variant="subtitle2">
                                  {row.name}
                                </Typography>
                              </TableCell>
                              <TableCell
                                sx={{
                                  width: '2.5%',
                                  minWidth: '30px',
                                  display: 'table-cell',
                                }}
                              >
                                <MuiSwitch
                                  checked={allSwitch?.name === row.name}
                                  onChange={(event: number) => handleSwitchChange(event, row)}
                                />
                              </TableCell>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      ))}
                      {quicksendlinks2?.map((row) => (
                        <TableContainer>
                          <Table key={row.id}>
                            <TableBody>
                              <TableCell>
                                <Typography sx={{ width: 'fit-content', cursor: 'pointer' }} variant="subtitle2">
                                  {row.name}
                                </Typography>
                              </TableCell>
                              <TableCell
                                sx={{
                                  width: '2.5%',
                                  minWidth: '30px',
                                  display: 'table-cell',
                                }}
                              >
                                <MuiSwitch
                                  checked={active?.name === row.name}
                                  onChange={(event: number) => handleSwitch(event, row)}
                                />
                              </TableCell>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      ))}
                    </Grid>
                    <Grid item md={6} xs={12}>
                      {quicksendlinks?.map((row) => (
                        <TableContainer>
                          <Table key={row.id}>
                            <TableBody>
                              <TableCell>
                                <Typography sx={{ width: 'fit-content', cursor: 'pointer' }} variant="subtitle2">
                                  {row.name}
                                </Typography>
                              </TableCell>
                              <TableCell
                                sx={{
                                  width: '2.5%',
                                  minWidth: '30px',
                                  display: 'table-cell',
                                }}
                              >
                                <MuiSwitch
                                  checked={active?.name === row.name}
                                  onChange={(event: number) => handleSwitch(event, row)}
                                />
                              </TableCell>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      ))}
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            </Grid>
            <Box
              position="absolute"
              right="30px"
              bottom="30px"
              display={{ sm: 'flex' }}
              sx={{ justifyContent: { sm: 'right' }, pr: { lg: '5' } }}
            >
              <Stack spacing={2} direction="row">
                <Button fullWidth type="reset" variant="contained" color="secondary">
                  Cancel
                </Button>
                <Button fullWidth type="submit" variant="contained" color="primary">
                  {allSwitch === null && active === null ? 'Save' : 'Send'}
                </Button>
              </Stack>
            </Box>
          </form>
        </Card>
      </QuickSendRoot>
      {/* <TempDrawer
        handleDrawerOpen={handleSwitchChange}
        handleDrawerClose={handleDrawerClose}
        state={open}
        drawerData={drawerData}
        title="Select"
      /> */}
      {/* <TemporaryDrawer
        onClose={handleDrawerClose}
        Title="Select"
        state={open}
        DrawerContent={
          <TempDrawer
            handleDrawerOpen={handleSwitchChange}
            handleDrawerClose={handleDrawerClose}
            drawerData={drawerData}
          />
        }
      /> */}
      <Popup
        size="xl"
        title={`Send to ${active?.name}:`}
        state={open}
        onClose={handleClickClose}
        popupContent={<SendMessage content={active ? active.component : ''} messageId={messageTemp?.messageId} />}
      />
    </Page>
  );
}

export default QuickSend;
